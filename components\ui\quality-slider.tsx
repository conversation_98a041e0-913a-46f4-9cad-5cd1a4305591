"use client"

import React from 'react'
import { Label } from './label'

interface QualitySliderProps {
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
  step?: number
  disabled?: boolean
  label?: string
  showValue?: boolean
  className?: string
}

export function QualitySlider({
  value,
  onChange,
  min = 1,
  max = 100,
  step = 1,
  disabled = false,
  label = "Quality",
  showValue = true,
  className = ""
}: QualitySliderProps) {
  const getQualityLabel = (quality: number): string => {
    if (quality <= 30) return "Low"
    if (quality <= 60) return "Medium"
    if (quality <= 85) return "High"
    return "Maximum"
  }

  const getQualityColor = (quality: number): string => {
    if (quality <= 30) return "text-red-600"
    if (quality <= 60) return "text-yellow-600"
    if (quality <= 85) return "text-blue-600"
    return "text-green-600"
  }

  const getSliderColor = (quality: number): string => {
    if (quality <= 30) return "accent-red-500"
    if (quality <= 60) return "accent-yellow-500"
    if (quality <= 85) return "accent-blue-500"
    return "accent-green-500"
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Label and Value */}
      <div className="flex items-center justify-between">
        <Label htmlFor="quality-slider" className="font-medium text-gray-700">
          {label}
        </Label>
        {showValue && (
          <div className="flex items-center space-x-2">
            <span className={`text-sm font-semibold ${getQualityColor(value)}`}>
              {getQualityLabel(value)}
            </span>
            <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {value}%
            </span>
          </div>
        )}
      </div>

      {/* Slider */}
      <div className="relative">
        <input
          id="quality-slider"
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(parseInt(e.target.value))}
          disabled={disabled}
          className={`
            w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer
            disabled:opacity-50 disabled:cursor-not-allowed
            ${getSliderColor(value)}
          `}
          style={{
            background: `linear-gradient(to right, 
              #ef4444 0%, 
              #eab308 30%, 
              #3b82f6 60%, 
              #22c55e 85%, 
              #22c55e 100%
            )`
          }}
        />
        
        {/* Custom thumb styling with CSS */}
        <style jsx>{`
          input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: white;
            border: 3px solid ${value <= 30 ? '#ef4444' : value <= 60 ? '#eab308' : value <= 85 ? '#3b82f6' : '#22c55e'};
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.15s ease-in-out;
          }
          
          input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }
          
          input[type="range"]::-moz-range-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: white;
            border: 3px solid ${value <= 30 ? '#ef4444' : value <= 60 ? '#eab308' : value <= 85 ? '#3b82f6' : '#22c55e'};
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.15s ease-in-out;
          }
        `}</style>
      </div>

      {/* Quality Marks */}
      <div className="flex justify-between text-xs text-gray-400 px-1">
        <span>Low</span>
        <span>Medium</span>
        <span>High</span>
        <span>Max</span>
      </div>

      {/* Helper text */}
      {!disabled && (
        <p className="text-xs text-gray-500">
          Higher quality = larger file size. Lower quality = smaller file size.
        </p>
      )}
    </div>
  )
} 