"use client"

import { useState, useCallback, useEffect, useRef } from "react"
import { ArrowLeft, Upload, Download, FileImage, Sliders, Info, Check, X, Loader2 } from "lucide-react"
import Link from "next/link"
import imageCompression from 'browser-image-compression'

interface CompressedImage {
  original: File
  compressed: Blob | null
  originalSize: number
  compressedSize: number
  originalUrl: string
  compressedUrl: string
  compressionRatio: number
  status: 'processing' | 'completed' | 'error'
  error?: string
}

export default function ImageCompressor() {
  const [images, setImages] = useState<CompressedImage[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [quality, setQuality] = useState(80)
  const [maxSizeMB, setMaxSizeMB] = useState(1)
  const [targetSizeKB, setTargetSizeKB] = useState(200)
  const [useTargetSizeKB, setUseTargetSizeKB] = useState(true) // Default to KB selection
  const [outputFormat, setOutputFormat] = useState<'auto' | 'jpeg' | 'png' | 'webp'>('auto')
  const [useTargetSize, setUseTargetSize] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    )
    
    if (files.length > 0) {
      processImages(files)
    }
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      processImages(files)
    }
  }

  const processImages = async (files: File[]) => {
    setIsProcessing(true)
    
    const newImages: CompressedImage[] = files.map(file => ({
      original: file,
      compressed: null,
      originalSize: file.size,
      compressedSize: 0,
      originalUrl: URL.createObjectURL(file),
      compressedUrl: '',
      compressionRatio: 0,
      status: 'processing' as const,
    }))
    
    setImages(prev => [...prev, ...newImages])
    
    // Process each image sequentially to avoid performance issues
    for (let i = 0; i < newImages.length; i++) {
      try {
        const file = files[i]
        let compressedFile: Blob
        
                if (useTargetSizeKB) {
          // Target size in KB with precision
          compressedFile = await compressToTargetSize(file, targetSizeKB)
        } else {
          // Regular compression with optional MB target or quality setting
          const options: any = {
            maxSizeMB: useTargetSize ? maxSizeMB : undefined,
            maxWidthOrHeight: undefined, // Preserve original dimensions
            useWebWorker: false, // Disable for better performance
            quality: quality / 100,
            preserveExif: false,
          }

          // Handle output format
          if (outputFormat !== 'auto') {
            options.fileType = `image/${outputFormat}`
          }

          compressedFile = await imageCompression(file, options)
        }
        
        const compressedUrl = URL.createObjectURL(compressedFile)
        
        setImages(prev => prev.map((img, index) => 
          index === images.length + i 
            ? {
                ...img,
                compressed: compressedFile,
                compressedSize: compressedFile.size,
                compressedUrl,
                compressionRatio: Math.round((1 - compressedFile.size / img.originalSize) * 100),
                status: 'completed'
              }
            : img
        ))
        
        // Add delay to prevent overwhelming the browser
        if (i < newImages.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      } catch (error) {
        setImages(prev => prev.map((img, index) => 
          index === images.length + i 
            ? {
                ...img,
                status: 'error',
                error: error instanceof Error ? error.message : 'Compression failed'
              }
            : img
        ))
      }
    }
    
    setIsProcessing(false)
  }

  // Function to compress to exact target size in KB
  const compressToTargetSize = async (file: File, targetKB: number): Promise<Blob> => {
    const targetBytes = targetKB * 1024
    
    // Start with a reasonable quality and keep reducing until we get under target
    let quality = 80
    let attempts = 0
    const maxAttempts = 10
    
    while (attempts < maxAttempts && quality > 5) {
      const options: any = {
        maxWidthOrHeight: undefined, // Preserve original dimensions
        useWebWorker: false,
        quality: quality / 100,
        preserveExif: false,
      }

      // Always compress to JPEG for better compression
      options.fileType = 'image/jpeg'

      try {
        const compressedFile = await imageCompression(file, options)
        
        // Check if file size is acceptable
        if (compressedFile.size <= targetBytes) {
          // Success! File is under target
          return compressedFile
        }
        
        // File is still too big, reduce quality and try again
        const sizeRatio = compressedFile.size / targetBytes
        if (sizeRatio > 3) {
          quality -= 25
        } else if (sizeRatio > 2) {
          quality -= 20
        } else if (sizeRatio > 1.5) {
          quality -= 15
        } else {
          quality -= 10
        }
        
        attempts++
      } catch (error) {
        // If compression fails, reduce quality and try again
        quality -= 20
        attempts++
      }
    }
    
    // If we couldn't get under target, return a very compressed version
    try {
      const finalOptions: any = {
        maxWidthOrHeight: undefined,
        useWebWorker: false,
        quality: 0.1, // Very low quality to ensure small size
        preserveExif: false,
        fileType: 'image/jpeg'
      }
      
      const finalFile = await imageCompression(file, finalOptions)
      return finalFile
    } catch (error) {
      // If everything fails, return original
      return file
    }
  }

  const removeImage = (index: number) => {
    const image = images[index]
    URL.revokeObjectURL(image.originalUrl)
    if (image.compressedUrl) {
      URL.revokeObjectURL(image.compressedUrl)
    }
    setImages(prev => prev.filter((_, i) => i !== index))

    // Clear file input to allow re-uploading same files
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const downloadImage = (image: CompressedImage) => {
    if (!image.compressed) return
    
    const link = document.createElement('a')
    link.href = image.compressedUrl
    link.download = `compressed-${image.original.name}`
    link.click()
  }

  const downloadAll = () => {
    images.forEach(image => {
      if (image.status === 'completed' && image.compressed) {
        downloadImage(image)
      }
    })
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B'
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB'
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
  }

  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.originalUrl)
      if (image.compressedUrl) {
        URL.revokeObjectURL(image.compressedUrl)
      }
    })
    setImages([])

    // Clear file input to allow re-uploading same files
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Home
          </Link>
          
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
              <FileImage className="w-6 h-6 text-white" />
            </div>
            <span className="text-xl font-bold">Imgion AI</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tool Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl mb-4">
            <FileImage className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Image Compressor</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Reduce image file sizes without losing quality. Compress multiple images at once with custom settings.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Upload & Settings */}
          <div className="lg:col-span-2 space-y-6">
            {/* Upload Area */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-xl font-semibold mb-6 flex items-center">
                <Upload className="w-5 h-5 mr-2" />
                Upload Images
              </h2>
              
              <div
                onDragOver={(e) => {
                  e.preventDefault()
                  setIsDragging(true)
                }}
                onDragLeave={() => setIsDragging(false)}
                onDrop={handleDrop}
                className={`border-2 border-dashed rounded-xl p-12 text-center transition-all ${
                  isDragging 
                    ? 'border-red-500 bg-red-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-input"
                />
                
                <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Drag & drop images here
                </h3>
                <p className="text-gray-500 mb-4">or click to select files</p>
                
                <label
                  htmlFor="file-input"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg cursor-pointer hover:shadow-lg transition-all"
                >
                  Browse Files
                </label>
                
                <p className="text-sm text-gray-400 mt-4">
                  Supported: JPG, PNG, WebP, GIF, BMP, TIFF
                </p>
              </div>
            </div>

            {/* Settings */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-xl font-semibold mb-6 flex items-center">
                <Sliders className="w-5 h-5 mr-2" />
                Compression Settings
              </h2>
              
              <div className="space-y-6">
                {/* Quality Slider */}
                <div>
                  <label className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Image Quality
                    </span>
                    <span className="text-sm font-semibold text-red-500">
                      {quality}%
                    </span>
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="100"
                    value={quality}
                    onChange={(e) => setQuality(Number(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    style={{
                      background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${quality}%, #e5e7eb ${quality}%, #e5e7eb 100%)`
                    }}
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Low</span>
                    <span>High</span>
                  </div>
                </div>

                                {/* Target Size KB Toggle */}
                <div>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={useTargetSizeKB}
                      onChange={(e) => {
                        setUseTargetSizeKB(e.target.checked)
                        if (e.target.checked) setUseTargetSize(false)
                      }}
                      className="w-4 h-4 text-red-500 rounded focus:ring-red-500"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Set target file size (KB)
                    </span>
                  </label>
                  
                  {useTargetSizeKB && (
                    <div className="mt-3">
                      <label className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">
                          Target size (KB)
                        </span>
                        <span className="text-sm font-semibold text-red-500">
                          {targetSizeKB} KB
                        </span>
                      </label>
                      <input
                        type="range"
                        min="10"
                        max="1000"
                        step="10"
                        value={targetSizeKB}
                        onChange={(e) => setTargetSizeKB(Number(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${(targetSizeKB / 1000) * 100}%, #e5e7eb ${(targetSizeKB / 1000) * 100}%, #e5e7eb 100%)`
                        }}
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>10 KB</span>
                        <span>1000 KB</span>
                      </div>
                    </div>
                  )}
                </div>

                                 {/* Target Size MB Toggle */}
                 <div>
                   <label className="flex items-center space-x-3 cursor-pointer">
                     <input
                       type="checkbox"
                       checked={useTargetSize}
                       onChange={(e) => {
                         setUseTargetSize(e.target.checked)
                         if (e.target.checked) setUseTargetSizeKB(false)
                       }}
                       className="w-4 h-4 text-red-500 rounded focus:ring-red-500"
                     />
                     <span className="text-sm font-medium text-gray-700">
                       Set target file size (MB)
                     </span>
                   </label>
                  
                  {useTargetSize && (
                    <div className="mt-3">
                      <label className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">
                          Maximum size (MB)
                        </span>
                        <span className="text-sm font-semibold text-red-500">
                          {maxSizeMB} MB
                        </span>
                      </label>
                      <input
                        type="range"
                        min="0.1"
                        max="10"
                        step="0.1"
                        value={maxSizeMB}
                        onChange={(e) => setMaxSizeMB(Number(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${(maxSizeMB / 10) * 100}%, #e5e7eb ${(maxSizeMB / 10) * 100}%, #e5e7eb 100%)`
                        }}
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0.1 MB</span>
                        <span>10 MB</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Output Format */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Output Format
                  </label>
                  <select
                    value={outputFormat}
                    onChange={(e) => setOutputFormat(e.target.value as any)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="auto">Auto (Keep Original)</option>
                    <option value="jpeg">JPEG</option>
                    <option value="png">PNG</option>
                    <option value="webp">WebP</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Image List */}
            {images.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold">
                    Images ({images.length})
                  </h2>
                  <div className="flex space-x-2">
                    {images.some(img => img.status === 'completed') && (
                      <button
                        onClick={downloadAll}
                        className="px-4 py-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:shadow-lg transition-all flex items-center"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Download All
                      </button>
                    )}
                    <button
                      onClick={clearAll}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {images.map((image, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <img
                        src={image.originalUrl}
                        alt={image.original.name}
                        className="w-16 h-16 object-cover rounded"
                      />
                      
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 truncate">
                          {image.original.name}
                        </h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{formatFileSize(image.originalSize)}</span>
                          {image.status === 'completed' && (
                            <>
                              <span>→</span>
                              <span className="text-green-600 font-medium">
                                {formatFileSize(image.compressedSize)}
                              </span>
                              <span className="text-green-600 font-semibold">
                                -{image.compressionRatio}%
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {image.status === 'processing' && (
                          <Loader2 className="w-5 h-5 text-red-500 animate-spin" />
                        )}
                        
                        {image.status === 'completed' && (
                          <>
                            <button
                              onClick={() => downloadImage(image)}
                              className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                            >
                              <Download className="w-5 h-5" />
                            </button>
                            <Check className="w-5 h-5 text-green-600" />
                          </>
                        )}
                        
                        {image.status === 'error' && (
                          <div className="flex items-center text-red-500">
                            <X className="w-5 h-5" />
                            <span className="text-sm ml-1">{image.error}</span>
                          </div>
                        )}
                        
                        <button
                          onClick={() => removeImage(index)}
                          className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Right Panel - Features & Tips */}
          <div className="space-y-6">
            {/* Features */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4">Features</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Batch Compression</p>
                    <p className="text-sm text-gray-600">Compress multiple images at once</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Custom Quality</p>
                    <p className="text-sm text-gray-600">Adjust compression level from 10-100%</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Target File Size</p>
                    <p className="text-sm text-gray-600">Set precise target size in KB or MB</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Preserve Dimensions</p>
                    <p className="text-sm text-gray-600">Keep original width and height intact</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Format Conversion</p>
                    <p className="text-sm text-gray-600">Convert to JPEG, PNG, or WebP</p>
                  </div>
                </li>
              </ul>
            </div>

            {/* Tips */}
            <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-6">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-red-500 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Tips for best results:
                  </h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Use 80-90% quality for photos</li>
                    <li>• Use 60-80% for web images</li>
                    <li>• WebP format gives best compression</li>
                    <li>• PNG is best for images with transparency</li>
                    <li>• Target KB size for precise file limits</li>
                    <li>• Dimensions are always preserved</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Stats */}
            {images.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Compression Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Images</span>
                    <span className="font-semibold">{images.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Original Size</span>
                    <span className="font-semibold">
                      {formatFileSize(images.reduce((acc, img) => acc + img.originalSize, 0))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Compressed Size</span>
                    <span className="font-semibold text-green-600">
                      {formatFileSize(images.reduce((acc, img) => acc + img.compressedSize, 0))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Space Saved</span>
                    <span className="font-semibold text-green-600">
                      {formatFileSize(
                        images.reduce((acc, img) => acc + img.originalSize, 0) -
                        images.reduce((acc, img) => acc + img.compressedSize, 0)
                      )}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}