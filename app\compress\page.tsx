"use client"

import { useState, useCallback, useRef } from "react"
import { ArrowLeft, Upload, Download, FileImage, Sliders, Info, Check, X, Loader2, Zap, Settings, Eye, EyeOff } from "lucide-react"
import Link from "next/link"
import imageCompression from 'browser-image-compression'

interface CompressedImage {
  original: File
  compressed: Blob | null
  originalSize: number
  compressedSize: number
  originalUrl: string
  compressedUrl: string
  compressionRatio: number
  status: 'processing' | 'completed' | 'error'
  error?: string
}

export default function ImageCompressor() {
  const [images, setImages] = useState<CompressedImage[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [quality, setQuality] = useState(80)
  const [maxSizeMB, setMaxSizeMB] = useState(1)
  const [targetSizeKB, setTargetSizeKB] = useState(100)
  const [useTargetSizeKB, setUseTargetSizeKB] = useState(true)
  const [outputFormat, setOutputFormat] = useState<'auto' | 'jpeg' | 'png' | 'webp'>('auto')
  const [useTargetSize, setUseTargetSize] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [previewMode, setPreviewMode] = useState<'grid' | 'list'>('grid')
  const fileInputRef = useRef<HTMLInputElement>(null)



  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    )
    
    if (files.length > 0) {
      processImages(files)
    }
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      processImages(files)
    }
  }

  const processImages = async (files: File[]) => {
    setIsProcessing(true)

    const newImages: CompressedImage[] = files.map(file => ({
      original: file,
      compressed: null,
      originalSize: file.size,
      compressedSize: 0,
      originalUrl: URL.createObjectURL(file),
      compressedUrl: '',
      compressionRatio: 0,
      status: 'processing' as const,
    }))
    
    setImages(prev => [...prev, ...newImages])
    
    // Process each image sequentially to avoid performance issues
    for (let i = 0; i < newImages.length; i++) {
      try {
        const file = files[i]
        let compressedFile: Blob

        if (useTargetSizeKB) {
          // Use browser-based compression for precise KB compression
          compressedFile = await compressToTargetSize(file, targetSizeKB)
        } else {
          // Regular compression with optional MB target or quality setting
          const options: any = {
            maxSizeMB: useTargetSize ? maxSizeMB : undefined,
            maxWidthOrHeight: undefined, // Preserve original dimensions
            useWebWorker: false, // Disable for better performance
            quality: quality / 100,
            preserveExif: false,
          }

          // Handle output format
          if (outputFormat !== 'auto') {
            options.fileType = `image/${outputFormat}`
          }

          compressedFile = await imageCompression(file, options)
        }
        
        const compressedUrl = URL.createObjectURL(compressedFile)
        
        setImages(prev => prev.map((img, index) => 
          index === images.length + i 
            ? {
                ...img,
                compressed: compressedFile,
                compressedSize: compressedFile.size,
                compressedUrl,
                compressionRatio: Math.round((1 - compressedFile.size / img.originalSize) * 100),
                status: 'completed'
              }
            : img
        ))
        
        // Add delay to prevent overwhelming the browser
        if (i < newImages.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      } catch (error) {
        setImages(prev => prev.map((img, index) => 
          index === images.length + i 
            ? {
                ...img,
                status: 'error',
                error: error instanceof Error ? error.message : 'Compression failed'
              }
            : img
        ))
      }
    }

    setIsProcessing(false)
  }

  // Function to compress to exact target size in KB with VERY aggressive compression
  const compressToTargetSize = async (file: File, targetKB: number): Promise<Blob> => {
    const targetBytes = targetKB * 1024
    console.log(`Starting compression: ${file.name}, target: ${targetKB}KB (${targetBytes} bytes)`)

    // Try multiple aggressive compression levels
    const qualityLevels = [0.1, 0.05, 0.03, 0.02, 0.01]

    for (const quality of qualityLevels) {
      try {
        const options: any = {
          maxWidthOrHeight: undefined,
          useWebWorker: false,
          quality: quality,
          preserveExif: false,
          fileType: 'image/jpeg'
        }

        console.log(`Trying quality: ${quality * 100}%`)
        const compressed = await imageCompression(file, options)
        console.log(`Result size: ${compressed.size} bytes (${(compressed.size/1024).toFixed(1)}KB)`)

        // If we got under target or this is our last attempt, return it
        if (compressed.size <= targetBytes || quality === qualityLevels[qualityLevels.length - 1]) {
          console.log(`Final result: ${(compressed.size/1024).toFixed(1)}KB`)
          return compressed
        }
      } catch (error) {
        console.error(`Compression failed at quality ${quality}:`, error)
        continue
      }
    }

    // If all fails, return original
    console.log('All compression attempts failed, returning original')
    return file
  }

  const removeImage = (index: number) => {
    const image = images[index]
    URL.revokeObjectURL(image.originalUrl)
    if (image.compressedUrl) {
      URL.revokeObjectURL(image.compressedUrl)
    }
    setImages(prev => prev.filter((_, i) => i !== index))

    // Clear file input to allow re-uploading same files
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const downloadImage = (image: CompressedImage) => {
    if (!image.compressed) return
    
    const link = document.createElement('a')
    link.href = image.compressedUrl
    link.download = `compressed-${image.original.name}`
    link.click()
  }

  const downloadAll = () => {
    images.forEach(image => {
      if (image.status === 'completed' && image.compressed) {
        downloadImage(image)
      }
    })
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B'
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB'
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
  }

  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.originalUrl)
      if (image.compressedUrl) {
        URL.revokeObjectURL(image.compressedUrl)
      }
    })
    setImages([])

    // Clear file input to allow re-uploading same files
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center text-slate-600 hover:text-slate-900 transition-all duration-200 group">
              <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              <span className="font-medium">Back</span>
            </Link>

            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Zap className="w-4 h-4 text-white" />
              </div>
              <span className="text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Imgion AI
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tool Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mb-4 shadow-lg shadow-blue-500/25">
            <FileImage className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-slate-900 mb-2">Smart Compress</h1>
          <p className="text-slate-600 max-w-xl mx-auto">
            Advanced compression with precise size control and format optimization
          </p>
        </div>

        <div className="space-y-6">
          {/* Upload Area */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-6 shadow-xl shadow-slate-900/5">
            <div
              onDragOver={(e) => {
                e.preventDefault()
                setIsDragging(true)
              }}
              onDragLeave={() => setIsDragging(false)}
              onDrop={handleDrop}
              className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                isDragging
                  ? 'border-blue-400 bg-blue-50/50 scale-[1.02]'
                  : 'border-slate-300 hover:border-slate-400 hover:bg-slate-50/50'
              }`}
            >
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileSelect}
                className="hidden"
                id="file-input"
                ref={fileInputRef}
              />

              {isProcessing ? (
                <div className="flex flex-col items-center">
                  <Loader2 className="w-8 h-8 text-blue-500 animate-spin mb-3" />
                  <p className="text-slate-600 font-medium">Processing images...</p>
                </div>
              ) : (
                <>
                  <Upload className="w-10 h-10 mx-auto mb-3 text-slate-400" />
                  <h3 className="text-lg font-semibold text-slate-900 mb-1">
                    Drop images here
                  </h3>
                  <p className="text-slate-500 text-sm mb-4">PNG, JPEG, WebP supported</p>
                </>
              )}

              <label
                htmlFor="file-input"
                className="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg cursor-pointer hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200 disabled:opacity-50"
              >
                <Upload className="w-4 h-4 mr-2" />
                Choose Files
              </label>
            </div>
          </div>

          {/* Settings */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-6 shadow-xl shadow-slate-900/5">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-slate-900 flex items-center">
                <Settings className="w-5 h-5 mr-2 text-blue-500" />
                Settings
              </h2>
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
              >
                {showAdvanced ? 'Simple' : 'Advanced'}
              </button>
            </div>

            <div className="space-y-4">
              {/* Target Size Toggle */}
              <div className="flex items-center justify-between p-3 bg-slate-50/50 rounded-lg border border-slate-200/60">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={useTargetSizeKB}
                    onChange={(e) => {
                      setUseTargetSizeKB(e.target.checked)
                      if (e.target.checked) setUseTargetSize(false)
                    }}
                    className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-sm font-medium text-slate-700">
                    Target Size Mode
                  </span>
                </div>
                {useTargetSizeKB && (
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    Precise
                  </span>
                )}
              </div>

              {useTargetSizeKB ? (
                <div className="space-y-3">
                  <div className="text-xs text-blue-700 bg-blue-50/50 p-3 rounded-lg border border-blue-200/60">
                    ✨ <strong>Precise Compression:</strong> Advanced browser-based compression with target size accuracy
                  </div>
                  <div className="flex items-center space-x-3">
                    <label className="text-sm text-slate-600 flex-shrink-0 font-medium">
                      Target size:
                    </label>
                    <input
                      type="number"
                      min="10"
                      max="5000"
                      value={targetSizeKB}
                      onChange={(e) => setTargetSizeKB(Number(e.target.value))}
                      className="flex-1 px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <span className="text-sm text-slate-500 font-medium">KB</span>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Quality Slider */}
                  <div>
                    <label className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-slate-700">
                        Quality
                      </span>
                      <span className="text-sm font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded">
                        {quality}%
                      </span>
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="100"
                      value={quality}
                      onChange={(e) => setQuality(Number(e.target.value))}
                      className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${quality}%, #e2e8f0 ${quality}%, #e2e8f0 100%)`
                      }}
                    />
                    <div className="flex justify-between text-xs text-slate-500 mt-1">
                      <span>Low</span>
                      <span>High</span>
                    </div>
                  </div>

                  {showAdvanced && (
                    <div className="space-y-4 pt-4 border-t border-slate-200">
                      {/* Output Format */}
                      <div>
                        <label className="text-sm font-medium text-slate-700 mb-2 block">
                          Output Format
                        </label>
                        <select
                          value={outputFormat}
                          onChange={(e) => setOutputFormat(e.target.value as any)}
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="auto">Auto (Keep Original)</option>
                          <option value="jpeg">JPEG</option>
                          <option value="png">PNG</option>
                          <option value="webp">WebP</option>
                        </select>
                      </div>

                      {/* Max Size MB */}
                      <div className="flex items-center justify-between p-3 bg-slate-50/50 rounded-lg border border-slate-200/60">
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={useTargetSize}
                            onChange={(e) => {
                              setUseTargetSize(e.target.checked)
                              if (e.target.checked) setUseTargetSizeKB(false)
                            }}
                            className="w-4 h-4 text-blue-500 rounded focus:ring-blue-500 focus:ring-2"
                          />
                          <span className="text-sm font-medium text-slate-700">
                            Max Size (MB)
                          </span>
                        </div>
                        {useTargetSize && (
                          <input
                            type="number"
                            min="0.1"
                            max="10"
                            step="0.1"
                            value={maxSizeMB}
                            onChange={(e) => setMaxSizeMB(Number(e.target.value))}
                            className="w-20 px-2 py-1 border border-slate-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

            </div>
          </div>

          {/* Images Preview */}
          {images.length > 0 && (
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-6 shadow-xl shadow-slate-900/5">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <h2 className="text-lg font-semibold text-slate-900">
                    Results
                  </h2>
                  <span className="px-2 py-1 bg-slate-100 text-slate-600 text-sm rounded-full font-medium">
                    {images.length}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setPreviewMode(previewMode === 'grid' ? 'list' : 'grid')}
                    className="p-2 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-lg transition-all"
                  >
                    {previewMode === 'grid' ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                  </button>
                  {images.some(img => img.status === 'completed') && (
                    <button
                      onClick={downloadAll}
                      className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-medium rounded-lg hover:shadow-lg hover:shadow-blue-500/25 transition-all flex items-center"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download All
                    </button>
                  )}
                  <button
                    onClick={clearAll}
                    className="px-4 py-2 border border-slate-300 text-slate-600 text-sm font-medium rounded-lg hover:bg-slate-50 transition-all"
                  >
                    Clear
                  </button>
                </div>
              </div>

              <div className={`${previewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-4' : 'space-y-3'}`}>
                {images.map((image, index) => (
                  <div
                    key={index}
                    className="group bg-slate-50/50 border border-slate-200/60 rounded-xl p-4 hover:shadow-md transition-all duration-200"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <img
                          src={image.originalUrl}
                          alt={image.original.name}
                          className="w-12 h-12 object-cover rounded-lg border border-slate-200"
                        />
                        {image.status === 'processing' && (
                          <div className="absolute inset-0 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
                          </div>
                        )}
                        {image.status === 'completed' && (
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                            <Check className="w-2.5 h-2.5 text-white" />
                          </div>
                        )}
                        {image.status === 'error' && (
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                            <X className="w-2.5 h-2.5 text-white" />
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-slate-900 truncate text-sm">
                          {image.original.name}
                        </h4>
                        <div className="flex items-center space-x-2 text-xs text-slate-500 mt-1">
                          <span>{formatFileSize(image.originalSize)}</span>
                          {image.status === 'completed' && (
                            <>
                              <span>→</span>
                              <span className={`font-medium ${
                                useTargetSizeKB && image.compressedSize <= targetSizeKB * 1024 * 1.1
                                  ? 'text-green-600'
                                  : useTargetSizeKB
                                    ? 'text-orange-600'
                                    : 'text-green-600'
                              }`}>
                                {formatFileSize(image.compressedSize)}
                              </span>
                              <span className="text-green-600 font-medium">
                                -{image.compressionRatio}%
                              </span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {image.status === 'completed' && (
                          <button
                            onClick={() => downloadImage(image)}
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                        )}

                        {image.status === 'error' && (
                          <div className="text-red-500 text-xs">
                            Error
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Batch Compression</p>
                    <p className="text-sm text-gray-600">Compress multiple images at once</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Custom Quality</p>
                    <p className="text-sm text-gray-600">Adjust compression level from 10-100%</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Target File Size</p>
                    <p className="text-sm text-gray-600">Set precise target size in KB or MB</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">🔒 Preserve Dimensions</p>
                    <p className="text-sm text-gray-600">Original width and height are NEVER changed - only file size is reduced</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Format Conversion</p>
                    <p className="text-sm text-gray-600">Convert to JPEG, PNG, or WebP</p>
                  </div>
                </li>
              </ul>
            </div>

            {/* Tips */}
            <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-6">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-red-500 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Tips for best results:
                  </h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Use 80-90% quality for photos</li>
                    <li>• Use 60-80% for web images</li>
                    <li>• WebP format gives best compression</li>
                    <li>• PNG is best for images with transparency</li>
                    <li>• Target KB size for precise file limits</li>
                    <li>• 🔒 Original dimensions are NEVER changed</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Stats */}
            {images.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-4">Compression Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Images</span>
                    <span className="font-semibold">{images.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Original Size</span>
                    <span className="font-semibold">
                      {formatFileSize(images.reduce((acc, img) => acc + img.originalSize, 0))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Compressed Size</span>
                    <span className="font-semibold text-green-600">
                      {formatFileSize(images.reduce((acc, img) => acc + img.compressedSize, 0))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Space Saved</span>
                    <span className="font-semibold text-green-600">
                      {formatFileSize(
                        images.reduce((acc, img) => acc + img.originalSize, 0) -
                        images.reduce((acc, img) => acc + img.compressedSize, 0)
                      )}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}