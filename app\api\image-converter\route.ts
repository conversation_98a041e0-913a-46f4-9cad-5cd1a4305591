import { NextRequest, NextResponse } from 'next/server';
import { 
  validateServerFile, 
  convertImageFormat,
  convertImagesInZip,
  extractImagesFromZip, 
  ProcessedImage
} from '@/lib/serverUtils';

const SUPPORTED_IMAGE_FORMATS = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/bmp', 'image/tiff', 'image/avif', 'image/heic'];
const TARGET_FORMATS = ['jpg', 'png', 'webp', 'avif', 'tiff'] as const;

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const targetFormat = formData.get('targetFormat') as string;
    const quality = parseInt(formData.get('quality') as string) || 80;

    // Validate inputs
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files uploaded' },
        { status: 400 }
      );
    }

    if (!TARGET_FORMATS.includes(targetFormat as typeof TARGET_FORMATS[number])) {
      return NextResponse.json(
        { error: `Unsupported target format: ${targetFormat}. Supported: ${TARGET_FORMATS.join(', ')}` },
        { status: 400 }
      );
    }

    if (quality < 1 || quality > 100) {
      return NextResponse.json(
        { error: 'Quality must be between 1 and 100' },
        { status: 400 }
      );
    }

    const processedImages: ProcessedImage[] = [];

    // Process each uploaded file
    for (const file of files) {
      // Validate file
      const validation = validateServerFile(file);
      if (!validation.isValid) {
        return NextResponse.json(
          { error: validation.error },
          { status: 400 }
        );
      }

      const buffer = Buffer.from(await file.arrayBuffer());

      // Handle ZIP files (support multiple MIME types)
      const isZipFile = ['application/zip', 'application/x-zip-compressed', 'application/octet-stream'].includes(file.type) || 
                        file.name.toLowerCase().endsWith('.zip');
      
      if (isZipFile) {
        try {
          const imagesFromZip = await extractImagesFromZip(buffer);
          
          if (imagesFromZip.length === 0) {
            return NextResponse.json(
              { error: 'ZIP archive has no valid image files to convert.' },
              { status: 400 }
            );
          }
          
          processedImages.push(...imagesFromZip);
        } catch (error) {
          console.error('Error extracting ZIP:', error);
          return NextResponse.json(
            { error: 'Failed to extract images from ZIP file. Please ensure it\'s a valid ZIP archive.' },
            { status: 400 }
          );
        }
      }
      // Handle RAR and other unsupported archives
      else if (file.name.toLowerCase().endsWith('.rar') || 
               file.name.toLowerCase().endsWith('.7z') || 
               file.name.toLowerCase().endsWith('.tar') ||
               file.name.toLowerCase().endsWith('.gz')) {
        return NextResponse.json(
          { error: 'Only .zip files are supported.' },
          { status: 400 }
        );
      }
      // Handle individual image files
      else if (SUPPORTED_IMAGE_FORMATS.includes(file.type)) {
        processedImages.push({
          buffer,
          originalName: file.name,
          mimetype: file.type,
          size: file.size
        });
      }
      else {
        return NextResponse.json(
          { error: `Unsupported file type: ${file.type}` },
          { status: 400 }
        );
      }
    }

    // Check if we have any images to process
    if (processedImages.length === 0) {
      return NextResponse.json(
        { error: 'No valid images found in uploaded files' },
        { status: 400 }
      );
    }

    // If single image, convert and return
    if (processedImages.length === 1) {
      const image = processedImages[0];
      const convertedBuffer = await convertImageFormat(
        image.buffer, 
        targetFormat as 'jpg' | 'png' | 'webp' | 'avif' | 'tiff', 
        quality
      );

      // Generate filename
      const nameWithoutExt = image.originalName.replace(/\.[^/.]+$/, '');
      const filename = `${nameWithoutExt}.${targetFormat}`;

      // Return the converted image
      return new NextResponse(convertedBuffer, {
        status: 200,
        headers: {
          'Content-Type': `image/${targetFormat}`,
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': convertedBuffer.length.toString(),
          'Cache-Control': 'no-cache',
        },
      });
    }

    // Multiple images - convert and package into ZIP
    const zipBuffer = await convertImagesInZip(processedImages, targetFormat as 'jpg' | 'png' | 'webp' | 'avif' | 'tiff', quality);

    // Generate filename for the ZIP
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `converted-images-${targetFormat}-${timestamp}.zip`;

    // Return the ZIP file
    return new NextResponse(zipBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': zipBuffer.length.toString(),
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Error in image-converter API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error during image conversion',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// GET endpoint for API info
export async function GET() {
  return NextResponse.json({
    endpoint: '/api/image-converter',
    method: 'POST',
    description: 'Convert images between different formats',
    acceptedFormats: SUPPORTED_IMAGE_FORMATS,
    targetFormats: TARGET_FORMATS,
    maxFileSize: '10MB per file',
    parameters: {
      files: 'File[] - Image files or ZIP containing images',
      targetFormat: `string - Target format (${TARGET_FORMATS.join('|')})`,
      quality: 'number (optional) - Quality 1-100 for lossy formats, default: 80'
    },
    response: 'Converted image file or ZIP containing converted images'
  });
} 