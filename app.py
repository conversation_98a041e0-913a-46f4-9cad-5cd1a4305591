import streamlit as st
import io
import os
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np
from streamlit_option_menu import option_menu

# Page config
st.set_page_config(
    page_title="ImageAI - Python Image Processing Suite",
    page_icon="🖼️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 2rem;
    }
    .tool-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    .success-box {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def compress_image_to_target_kb(image, target_kb):
    """
    Ultra-aggressive compression to achieve exact target size in KB
    """
    target_bytes = target_kb * 1024
    
    # Convert to RGB if needed
    if image.mode in ('RGBA', 'LA', 'P'):
        image = image.convert('RGB')
    
    best_result = None
    best_size = float('inf')
    
    # Try multiple quality levels with binary search
    min_quality = 1
    max_quality = 95
    attempts = 0
    max_attempts = 20
    
    while attempts < max_attempts and min_quality <= max_quality:
        current_quality = (min_quality + max_quality) // 2
        
        # Compress with current quality
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG', quality=current_quality, optimize=True)
        current_size = buffer.tell()
        
        # Track best result
        if abs(current_size - target_bytes) < abs(best_size - target_bytes):
            best_result = buffer.getvalue()
            best_size = current_size
        
        # Adjust search range
        if current_size > target_bytes:
            max_quality = current_quality - 1
        else:
            min_quality = current_quality + 1
        
        # If very close to target, accept it
        if abs(current_size - target_bytes) < target_bytes * 0.05:
            return buffer.getvalue(), current_size
        
        attempts += 1
    
    # If still not close enough, try extreme compression
    if best_size > target_bytes * 1.2:
        for quality in [10, 8, 5, 3, 1]:
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=quality, optimize=True)
            size = buffer.tell()
            
            if size <= target_bytes or abs(size - target_bytes) < abs(best_size - target_bytes):
                best_result = buffer.getvalue()
                best_size = size
                
                if size <= target_bytes:
                    break
    
    return best_result, best_size

def convert_image_format(image, target_format):
    """Convert image to target format"""
    buffer = io.BytesIO()
    
    if target_format.upper() == 'JPEG' and image.mode in ('RGBA', 'LA', 'P'):
        image = image.convert('RGB')
    
    image.save(buffer, format=target_format.upper(), quality=95, optimize=True)
    return buffer.getvalue()

def resize_image(image, width=None, height=None, maintain_aspect=True):
    """Resize image with optional aspect ratio maintenance"""
    original_width, original_height = image.size
    
    if maintain_aspect:
        if width and not height:
            height = int((width / original_width) * original_height)
        elif height and not width:
            width = int((height / original_height) * original_width)
        elif width and height:
            # Fit within bounds while maintaining aspect ratio
            ratio = min(width / original_width, height / original_height)
            width = int(original_width * ratio)
            height = int(original_height * ratio)
    
    return image.resize((width, height), Image.Resampling.LANCZOS)

def main():
    st.markdown('<h1 class="main-header">🖼️ ImageAI - Python Suite</h1>', unsafe_allow_html=True)
    
    # Sidebar menu
    with st.sidebar:
        selected = option_menu(
            "Image Tools",
            ["🗜️ Compress", "🔄 Convert", "📏 Resize", "✨ Enhance"],
            icons=['file-zip', 'arrow-repeat', 'aspect-ratio', 'stars'],
            menu_icon="image",
            default_index=0,
            styles={
                "container": {"padding": "0!important", "background-color": "#fafafa"},
                "icon": {"color": "#ff6b6b", "font-size": "18px"},
                "nav-link": {"font-size": "16px", "text-align": "left", "margin": "0px"},
                "nav-link-selected": {"background-color": "#ff6b6b"},
            }
        )
    
    # File uploader
    uploaded_files = st.file_uploader(
        "Upload Images",
        type=['png', 'jpg', 'jpeg', 'webp', 'bmp', 'tiff'],
        accept_multiple_files=True,
        help="Drag and drop images here or click to browse"
    )
    
    if uploaded_files:
        if selected == "🗜️ Compress":
            compress_tool(uploaded_files)
        elif selected == "🔄 Convert":
            convert_tool(uploaded_files)
        elif selected == "📏 Resize":
            resize_tool(uploaded_files)
        elif selected == "✨ Enhance":
            enhance_tool(uploaded_files)

def compress_tool(uploaded_files):
    st.markdown('<div class="tool-card">', unsafe_allow_html=True)
    st.header("🗜️ Ultra-Aggressive Image Compression")
    
    # Compression settings
    col1, col2 = st.columns(2)
    
    with col1:
        target_kb = st.number_input(
            "Target Size (KB)",
            min_value=10,
            max_value=5000,
            value=100,
            step=10,
            help="Exact target file size in KB"
        )
    
    with col2:
        output_format = st.selectbox(
            "Output Format",
            ["JPEG", "PNG", "WEBP"],
            index=0,
            help="Output image format"
        )
    
    if st.button("🚀 Compress Images", type="primary"):
        progress_bar = st.progress(0)
        results = []
        
        for i, uploaded_file in enumerate(uploaded_files):
            # Load image
            image = Image.open(uploaded_file)
            original_size = len(uploaded_file.getvalue())
            
            st.write(f"**Processing:** {uploaded_file.name}")
            st.write(f"**Original Size:** {original_size/1024:.1f} KB")
            
            # Compress
            if output_format == "JPEG":
                compressed_data, final_size = compress_image_to_target_kb(image, target_kb)
            else:
                # For PNG/WEBP, convert first then compress
                buffer = io.BytesIO()
                if image.mode in ('RGBA', 'LA', 'P') and output_format == "JPEG":
                    image = image.convert('RGB')
                image.save(buffer, format=output_format, optimize=True)
                compressed_data = buffer.getvalue()
                final_size = len(compressed_data)
            
            # Results
            compression_ratio = ((original_size - final_size) / original_size) * 100
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Original", f"{original_size/1024:.1f} KB")
            with col2:
                st.metric("Compressed", f"{final_size/1024:.1f} KB")
            with col3:
                st.metric("Saved", f"{compression_ratio:.1f}%")
            
            # Success indicator
            if abs(final_size/1024 - target_kb) <= target_kb * 0.1:
                st.markdown(f'<div class="success-box">✅ Target achieved! Got {final_size/1024:.1f}KB (target: {target_kb}KB)</div>', unsafe_allow_html=True)
            else:
                st.warning(f"⚠️ Close result: {final_size/1024:.1f}KB (target: {target_kb}KB)")
            
            # Download button
            file_extension = output_format.lower()
            if file_extension == "jpeg":
                file_extension = "jpg"
            
            filename = f"compressed_{uploaded_file.name.split('.')[0]}.{file_extension}"
            
            st.download_button(
                label=f"📥 Download {filename}",
                data=compressed_data,
                file_name=filename,
                mime=f"image/{file_extension}",
                key=f"download_{i}"
            )
            
            st.divider()
            progress_bar.progress((i + 1) / len(uploaded_files))
    
    st.markdown('</div>', unsafe_allow_html=True)

def convert_tool(uploaded_files):
    st.markdown('<div class="tool-card">', unsafe_allow_html=True)
    st.header("🔄 Image Format Converter")

    # Conversion settings
    col1, col2 = st.columns(2)

    with col1:
        target_format = st.selectbox(
            "Convert To",
            ["JPEG", "PNG", "WEBP", "BMP", "TIFF"],
            help="Target image format"
        )

    with col2:
        quality = st.slider(
            "Quality (for JPEG/WEBP)",
            min_value=10,
            max_value=100,
            value=95,
            help="Image quality (higher = better quality, larger file)"
        )

    if st.button("🔄 Convert Images", type="primary"):
        progress_bar = st.progress(0)

        for i, uploaded_file in enumerate(uploaded_files):
            image = Image.open(uploaded_file)
            original_format = image.format or "Unknown"

            st.write(f"**Converting:** {uploaded_file.name}")
            st.write(f"**From:** {original_format} **To:** {target_format}")

            # Convert
            buffer = io.BytesIO()

            # Handle transparency for JPEG
            if target_format == 'JPEG' and image.mode in ('RGBA', 'LA', 'P'):
                # Create white background
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background

            # Save with appropriate settings
            save_kwargs = {'format': target_format, 'optimize': True}
            if target_format in ['JPEG', 'WEBP']:
                save_kwargs['quality'] = quality

            image.save(buffer, **save_kwargs)
            converted_data = buffer.getvalue()

            # File size comparison
            original_size = len(uploaded_file.getvalue())
            new_size = len(converted_data)

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Original", f"{original_size/1024:.1f} KB")
            with col2:
                st.metric("Converted", f"{new_size/1024:.1f} KB")
            with col3:
                change = ((new_size - original_size) / original_size) * 100
                st.metric("Size Change", f"{change:+.1f}%")

            # Download button
            file_extension = target_format.lower()
            if file_extension == "jpeg":
                file_extension = "jpg"

            filename = f"converted_{uploaded_file.name.split('.')[0]}.{file_extension}"

            st.download_button(
                label=f"📥 Download {filename}",
                data=converted_data,
                file_name=filename,
                mime=f"image/{file_extension}",
                key=f"convert_{i}"
            )

            st.divider()
            progress_bar.progress((i + 1) / len(uploaded_files))

    st.markdown('</div>', unsafe_allow_html=True)

def resize_tool(uploaded_files):
    st.markdown('<div class="tool-card">', unsafe_allow_html=True)
    st.header("📏 Image Resizer")

    # Resize settings
    col1, col2, col3 = st.columns(3)

    with col1:
        resize_mode = st.selectbox(
            "Resize Mode",
            ["Custom Size", "Percentage", "Preset Sizes"],
            help="How to resize the image"
        )

    with col2:
        maintain_aspect = st.checkbox(
            "Maintain Aspect Ratio",
            value=True,
            help="Keep original proportions"
        )

    with col3:
        resampling = st.selectbox(
            "Quality",
            ["High (Lanczos)", "Medium (Bilinear)", "Fast (Nearest)"],
            help="Resampling algorithm"
        )

    # Resize parameters based on mode
    if resize_mode == "Custom Size":
        col1, col2 = st.columns(2)
        with col1:
            new_width = st.number_input("Width (px)", min_value=1, max_value=10000, value=800)
        with col2:
            new_height = st.number_input("Height (px)", min_value=1, max_value=10000, value=600)

    elif resize_mode == "Percentage":
        scale_percent = st.slider("Scale (%)", min_value=10, max_value=500, value=100)

    else:  # Preset Sizes
        preset = st.selectbox(
            "Preset Size",
            ["Instagram Square (1080x1080)", "Instagram Story (1080x1920)",
             "Facebook Cover (1200x630)", "Twitter Header (1500x500)",
             "YouTube Thumbnail (1280x720)", "HD (1920x1080)", "4K (3840x2160)"]
        )

        preset_sizes = {
            "Instagram Square (1080x1080)": (1080, 1080),
            "Instagram Story (1080x1920)": (1080, 1920),
            "Facebook Cover (1200x630)": (1200, 630),
            "Twitter Header (1500x500)": (1500, 500),
            "YouTube Thumbnail (1280x720)": (1280, 720),
            "HD (1920x1080)": (1920, 1080),
            "4K (3840x2160)": (3840, 2160)
        }
        new_width, new_height = preset_sizes[preset]

    if st.button("📏 Resize Images", type="primary"):
        progress_bar = st.progress(0)

        # Resampling method
        resample_methods = {
            "High (Lanczos)": Image.Resampling.LANCZOS,
            "Medium (Bilinear)": Image.Resampling.BILINEAR,
            "Fast (Nearest)": Image.Resampling.NEAREST
        }
        resample_method = resample_methods[resampling]

        for i, uploaded_file in enumerate(uploaded_files):
            image = Image.open(uploaded_file)
            original_width, original_height = image.size

            st.write(f"**Resizing:** {uploaded_file.name}")
            st.write(f"**Original:** {original_width}x{original_height}")

            # Calculate new dimensions
            if resize_mode == "Percentage":
                target_width = int(original_width * scale_percent / 100)
                target_height = int(original_height * scale_percent / 100)
            else:
                target_width, target_height = new_width, new_height

            # Maintain aspect ratio if requested
            if maintain_aspect and resize_mode != "Percentage":
                ratio = min(target_width / original_width, target_height / original_height)
                target_width = int(original_width * ratio)
                target_height = int(original_height * ratio)

            # Resize image
            resized_image = image.resize((target_width, target_height), resample_method)

            st.write(f"**New Size:** {target_width}x{target_height}")

            # Save resized image
            buffer = io.BytesIO()
            format_name = image.format or 'JPEG'
            resized_image.save(buffer, format=format_name, quality=95, optimize=True)
            resized_data = buffer.getvalue()

            # Size comparison
            original_size = len(uploaded_file.getvalue())
            new_size = len(resized_data)

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Original", f"{original_size/1024:.1f} KB")
            with col2:
                st.metric("Resized", f"{new_size/1024:.1f} KB")
            with col3:
                change = ((new_size - original_size) / original_size) * 100
                st.metric("Size Change", f"{change:+.1f}%")

            # Download button
            filename = f"resized_{uploaded_file.name}"

            st.download_button(
                label=f"📥 Download {filename}",
                data=resized_data,
                file_name=filename,
                mime=f"image/{image.format.lower() if image.format else 'jpeg'}",
                key=f"resize_{i}"
            )

            st.divider()
            progress_bar.progress((i + 1) / len(uploaded_files))

    st.markdown('</div>', unsafe_allow_html=True)

def enhance_tool(uploaded_files):
    st.markdown('<div class="tool-card">', unsafe_allow_html=True)
    st.header("✨ Image Enhancer")

    # Enhancement settings
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("🎨 Basic Adjustments")
        brightness = st.slider("Brightness", 0.5, 2.0, 1.0, 0.1)
        contrast = st.slider("Contrast", 0.5, 2.0, 1.0, 0.1)
        saturation = st.slider("Saturation", 0.0, 2.0, 1.0, 0.1)
        sharpness = st.slider("Sharpness", 0.0, 2.0, 1.0, 0.1)

    with col2:
        st.subheader("🔧 Filters")
        apply_blur = st.checkbox("Blur")
        if apply_blur:
            blur_radius = st.slider("Blur Radius", 0.5, 5.0, 1.0, 0.5)

        apply_sharpen = st.checkbox("Sharpen")
        apply_edge_enhance = st.checkbox("Edge Enhance")
        apply_smooth = st.checkbox("Smooth")

        # Auto enhance options
        st.subheader("🤖 Auto Enhance")
        auto_contrast = st.checkbox("Auto Contrast")
        auto_color = st.checkbox("Auto Color Balance")

    if st.button("✨ Enhance Images", type="primary"):
        progress_bar = st.progress(0)

        for i, uploaded_file in enumerate(uploaded_files):
            image = Image.open(uploaded_file)
            enhanced_image = image.copy()

            st.write(f"**Enhancing:** {uploaded_file.name}")

            # Apply basic adjustments
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(enhanced_image)
                enhanced_image = enhancer.enhance(brightness)

            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(enhanced_image)
                enhanced_image = enhancer.enhance(contrast)

            if saturation != 1.0:
                enhancer = ImageEnhance.Color(enhanced_image)
                enhanced_image = enhancer.enhance(saturation)

            if sharpness != 1.0:
                enhancer = ImageEnhance.Sharpness(enhanced_image)
                enhanced_image = enhancer.enhance(sharpness)

            # Apply filters
            if apply_blur:
                enhanced_image = enhanced_image.filter(ImageFilter.GaussianBlur(radius=blur_radius))

            if apply_sharpen:
                enhanced_image = enhanced_image.filter(ImageFilter.SHARPEN)

            if apply_edge_enhance:
                enhanced_image = enhanced_image.filter(ImageFilter.EDGE_ENHANCE)

            if apply_smooth:
                enhanced_image = enhanced_image.filter(ImageFilter.SMOOTH)

            # Auto enhancements using OpenCV
            if auto_contrast or auto_color:
                # Convert PIL to OpenCV
                cv_image = cv2.cvtColor(np.array(enhanced_image), cv2.COLOR_RGB2BGR)

                if auto_contrast:
                    # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
                    lab = cv2.cvtColor(cv_image, cv2.COLOR_BGR2LAB)
                    l, a, b = cv2.split(lab)
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
                    l = clahe.apply(l)
                    cv_image = cv2.merge([l, a, b])
                    cv_image = cv2.cvtColor(cv_image, cv2.COLOR_LAB2BGR)

                if auto_color:
                    # Simple white balance
                    result = cv2.cvtColor(cv_image, cv2.COLOR_BGR2LAB)
                    avg_a = np.average(result[:, :, 1])
                    avg_b = np.average(result[:, :, 2])
                    result[:, :, 1] = result[:, :, 1] - ((avg_a - 128) * (result[:, :, 0] / 255.0) * 1.1)
                    result[:, :, 2] = result[:, :, 2] - ((avg_b - 128) * (result[:, :, 0] / 255.0) * 1.1)
                    cv_image = cv2.cvtColor(result, cv2.COLOR_LAB2BGR)

                # Convert back to PIL
                enhanced_image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))

            # Show before/after preview
            col1, col2 = st.columns(2)
            with col1:
                st.write("**Before:**")
                st.image(image, width=300)
            with col2:
                st.write("**After:**")
                st.image(enhanced_image, width=300)

            # Save enhanced image
            buffer = io.BytesIO()
            format_name = image.format or 'JPEG'
            enhanced_image.save(buffer, format=format_name, quality=95, optimize=True)
            enhanced_data = buffer.getvalue()

            # Size comparison
            original_size = len(uploaded_file.getvalue())
            new_size = len(enhanced_data)

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Original", f"{original_size/1024:.1f} KB")
            with col2:
                st.metric("Enhanced", f"{new_size/1024:.1f} KB")
            with col3:
                change = ((new_size - original_size) / original_size) * 100
                st.metric("Size Change", f"{change:+.1f}%")

            # Download button
            filename = f"enhanced_{uploaded_file.name}"

            st.download_button(
                label=f"📥 Download {filename}",
                data=enhanced_data,
                file_name=filename,
                mime=f"image/{image.format.lower() if image.format else 'jpeg'}",
                key=f"enhance_{i}"
            )

            st.divider()
            progress_bar.progress((i + 1) / len(uploaded_files))

    st.markdown('</div>', unsafe_allow_html=True)

if __name__ == "__main__":
    main()
