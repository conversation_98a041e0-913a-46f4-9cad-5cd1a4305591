"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import {
  Upload,
  Download,
  FileText,
  ImageIcon,
  FileArchiveIcon as Compress,
  Scissors,
  Maximize2,
  FileEdit,
  Type,
  Palette,
  Settings,
  CreditCard,
  RotateCw,
  Edit3,
  ArrowRight,
  Menu,
  X,
  Zap,
  Shield,
  Clock,
  MessageCircle,
  Check,
  Star,
  Github,
  Twitter,
  Linkedin,
  Mail,
  Sparkles,
  Bot,
  RefreshCw,
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

export default function ImgionAI() {
  const router = useRouter()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [chatbotOpen, setChatbotOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("image-to-pdf")
  const [showWelcomePopup, setShowWelcomePopup] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const tools = [
    {
      id: "image-to-pdf",
      title: "Image to PDF",
      icon: FileText,
      description: "Convert images to PDF documents instantly",
      color: "from-blue-500 to-purple-500",
      route: "/tools/image-to-pdf",
      available: true,
    },
    {
      id: "pdf-to-image",
      title: "PDF to Image",
      icon: ImageIcon,
      description: "Extract high-quality images from PDF files instantly",
      color: "from-orange-500 to-red-500",
      route: "/tools/pdf-to-image",
      available: false,
    },
    {
      id: "compressor",
      title: "Image Compressor",
      icon: Compress,
      description: "Reduce file size without losing quality",
      color: "from-red-500 to-pink-500",
      route: "/compress",
      available: true,
    },
    {
      id: "background-remover",
      title: "Background Remover",
      icon: Scissors,
      description: "Remove backgrounds with AI precision",
      color: "from-red-500 to-orange-500",
      route: "/tools/remove-bg",
      available: false,
    },
    {
      id: "resizer",
      title: "Image Resizer",
      icon: Maximize2,
      description: "Resize images while maintaining quality",
      color: "from-teal-500 to-cyan-500",
      route: "/tools/image-resizer",
      available: false,
    },
    {
      id: "pdf-editor",
      title: "Smart PDF Editor",
      icon: FileEdit,
      description: "Edit PDFs while preserving formatting",
      color: "from-indigo-500 to-purple-500",
      route: "/tools/pdf-editor",
      available: false,
    },
    {
      id: "text-editor",
      title: "AI Image Text Editor",
      icon: Type,
      description: "Auto-replace text in images seamlessly",
      color: "from-violet-500 to-purple-500",
      route: "/tools/text-editor",
      available: false,
    },
    {
      id: "photo-editor",
      title: "Photo Editor",
      icon: Palette,
      description: "Draw, crop, add icons, text, and filters",
      color: "from-pink-500 to-rose-500",
      route: "/tools/photo-editor",
      available: false,
    },
    {
      id: "dpi-changer",
      title: "DPI Changer",
      icon: Settings,
      description: "Adjust image resolution and DPI settings",
      color: "from-yellow-500 to-orange-500",
      route: "/tools/dpi-changer",
      available: false,
    },
    {
      id: "passport-generator",
      title: "Passport Size Generator",
      icon: CreditCard,
      description: "Create passport-sized photos instantly",
      color: "from-emerald-500 to-teal-500",
      route: "/tools/passport-generator",
      available: false,
    },
    {
      id: "image-converter",
      title: "Image Format Converter",
      icon: RefreshCw,
      description: "Convert JPG, PNG, WebP, AVIF and more — even from ZIP files",
      color: "from-green-500 to-blue-500",
      route: "/tools/image-converter",
      available: true,
    },
  ]

  const bonusTools = [
    { title: "Rotate/Flip Tool", icon: RotateCw, description: "Rotate and flip images easily" },
    { title: "Bulk Rename", icon: Edit3, description: "Rename multiple files at once" },
  ]

  const features = [
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Process images in seconds with our optimized AI algorithms",
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description: "Your images are processed locally and never stored on our servers",
    },
    {
      icon: Clock,
      title: "24/7 Available",
      description: "Access all tools anytime, anywhere from any device",
    },
  ]

  const faqs = [
    {
      question: "Is Imgion AI free?",
      answer:
        "Yes! Imgion AI offers a generous free tier with access to all basic tools. Premium features are available with our subscription plans for advanced users.",
    },
    {
      question: "Do you store any images?",
      answer:
        "No, we prioritize your privacy. All image processing happens locally in your browser, and we never store your images on our servers.",
    },
    {
      question: "Can I use this on mobile?",
      answer: "Our platform is fully responsive and works seamlessly on desktop, tablet, and mobile devices.",
    },
    {
      question: "What formats are supported?",
      answer:
        "We support all major image formats including JPG, PNG, WebP, GIF, BMP, TIFF, and PDF files for comprehensive editing.",
    },
    {
      question: "How accurate is AI replacement?",
      answer:
        "Our AI text replacement feature uses advanced machine learning to achieve 95%+ accuracy while maintaining original font styles and background context.",
    },
  ]

  const handleProcessing = () => {
    setProcessing(true)
    setTimeout(() => {
      setProcessing(false)
      setShowPreview(true)
    }, 3000)
  }

  const scrollToTools = () => {
    document.getElementById("tools")?.scrollIntoView({ behavior: "smooth" })
  }

  const handleToolClick = (tool: any) => {
    if (tool.available) {
      router.push(tool.route)
    } else {
      // Show coming soon message or sign up prompt
      alert(`${tool.title} is coming soon! 🚀`)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                {/* Modern Logo */}
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"></div>
                </div>
                <div className="ml-3">
                  <span className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    Imgion AI
                  </span>
                  <div className="text-xs text-gray-500 -mt-1">imgionai.com</div>
                </div>
              </div>
            </div>

            {/* Desktop Menu */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link
                  href="#"
                  className="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Home
                </Link>
                <Link
                  href="#features"
                  className="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Features
                </Link>
                <Link
                  href="#tools"
                  className="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Tools
                </Link>
                <Link
                  href="#faq"
                  className="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
                >
                  FAQ
                </Link>
                <Link
                  href="#contact"
                  className="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Contact
                </Link>
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button variant="ghost" size="sm" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
                {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden">
              <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <Link href="#" className="block px-3 py-2 text-base font-medium text-gray-900 hover:text-indigo-600">
                  Home
                </Link>
                <Link
                  href="#features"
                  className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600"
                >
                  Features
                </Link>
                <Link
                  href="#tools"
                  className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600"
                >
                  Tools
                </Link>
                <Link href="#faq" className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600">
                  FAQ
                </Link>
                <Link
                  href="#contact"
                  className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600"
                >
                  Contact
                </Link>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <Badge className="mb-6 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border-indigo-200">
            <Sparkles className="w-4 h-4 mr-2" />
            Powered by Advanced AI
          </Badge>
          <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight">
            Transform Your Images
            <br />
            <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              with Smart AI
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
            Edit, convert, resize, compress, and enhance images with our powerful AI tools — all in one place.
            <br />
            <span className="font-semibold text-indigo-600">Smarter. Simpler. Sharper Images.</span>
          </p>
          <div className="space-x-4">
            <Button
              size="lg"
              onClick={() => router.push('/tools/image-to-pdf')}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-12 py-4 text-lg font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              Try Image to PDF
              <ArrowRight className="w-6 h-6 ml-2" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={scrollToTools}
              className="px-8 py-4 text-lg font-semibold rounded-full border-2 border-indigo-600 text-indigo-600 hover:bg-indigo-600 hover:text-white transition-all duration-300"
            >
              View All Tools
            </Button>
          </div>
          <p className="text-sm text-gray-500 mt-4">No credit card required • Process unlimited images</p>
        </div>

        {/* Floating AI Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full opacity-20 animate-pulse delay-2000"></div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">{tools.length} Powerful AI Tools</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Everything you need for professional image editing and processing
            </p>
          </div>

          {/* Main Tools Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
            {tools.map((tool, index) => {
              const IconComponent = tool.icon
              return (
                <Card
                  key={tool.id}
                  onClick={() => handleToolClick(tool)}
                  className={`text-center p-6 transition-all duration-300 border-0 shadow-lg cursor-pointer group relative ${
                    tool.available 
                      ? 'hover:shadow-xl hover:scale-105' 
                      : 'opacity-75 hover:opacity-100'
                  }`}
                >
                  <CardContent className="pt-6">
                    {/* Availability Badge */}
                    {tool.available ? (
                      <div className="absolute top-2 right-2 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                    ) : (
                      <div className="absolute top-2 right-2 px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full border">
                        Soon
                      </div>
                    )}
                    
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${tool.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 ${
                        !tool.available ? 'opacity-75' : ''
                      }`}
                    >
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{tool.title}</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">{tool.description}</p>
                    
                    {tool.available && (
                      <div className="mt-3 text-xs text-indigo-600 font-medium">
                        Click to use →
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Bonus Tools */} 
          <div className="text-center mb-8">
            <Badge className="bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-700 border-orange-200">
              <Star className="w-4 h-4 mr-2" />
              Bonus Tools
            </Badge>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto mb-16">
            {bonusTools.map((tool, index) => {
              const IconComponent = tool.icon
              return (
                <Card
                  key={index}
                  className="text-center p-6 hover:shadow-lg transition-all duration-300 border-0 shadow-md hover:scale-105 cursor-pointer"
                >
                  <CardContent className="pt-6">
                    <div className="w-14 h-14 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-7 h-7 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{tool.title}</h3>
                    <p className="text-gray-600 text-sm">{tool.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Why Choose Us */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Tool Selector Section */}
      

      {/* How It Works */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Simple 3-step process to transform your images</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6 relative">
                <Upload className="w-10 h-10 text-white" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center text-xs font-bold text-gray-900">
                  1
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Upload</h3>
              <p className="text-gray-600 text-lg">
                Drag & drop or select image/PDF/ZIP files. Our platform supports all major formats.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 relative">
                <Settings className="w-10 h-10 text-white" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center text-xs font-bold text-gray-900">
                  2
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Configure</h3>
              <p className="text-gray-600 text-lg">
                Resize, compress, convert, edit — customize settings for your specific needs.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 relative">
                <Download className="w-10 h-10 text-white" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center text-xs font-bold text-gray-900">
                  3
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Download</h3>
              <p className="text-gray-600 text-lg">
                Instantly download processed files. High quality results every time.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing CTA */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Ready to Get Started?</h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of users who trust Imgion AI for their image processing needs
          </p>
          <div className="space-y-4">
            <Button
              size="lg"
              onClick={() => router.push('/tools/image-to-pdf')}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-12 py-4 text-lg font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              Try Image to PDF Free
              <ArrowRight className="w-6 h-6 ml-2" />
            </Button>
            <div>
              <Button variant="link" className="text-indigo-600 hover:text-indigo-700">
                Upgrade for advanced tools →
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">Everything you need to know about Imgion AI</p>
          </div>

          <Accordion type="single" collapsible className="w-full space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="bg-gray-50 rounded-xl shadow-sm border-0">
                <AccordionTrigger className="px-6 py-4 text-left font-semibold text-gray-900 hover:no-underline">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4 text-gray-600">{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div className="ml-3">
                  <span className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                    Imgion AI
                  </span>
                  <div className="text-xs text-gray-400 -mt-1">imgionai.com</div>
                </div>
              </div>
              <p className="text-gray-400 mb-4 max-w-md">
                Transform your images with smart AI. Smarter. Simpler. Sharper Images.
              </p>
              <div className="flex space-x-4">
                <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                  <Twitter className="w-6 h-6" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                  <Github className="w-6 h-6" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                  <Linkedin className="w-6 h-6" />
                </Link>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="#features" className="text-gray-400 hover:text-white transition-colors">
                    Features
                  </Link>
                </li>
                <li>
                  <Link href="#tools" className="text-gray-400 hover:text-white transition-colors">
                    Tools
                  </Link>
                </li>
                <li>
                  <Link href="#faq" className="text-gray-400 hover:text-white transition-colors">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link href="#contact" className="text-gray-400 hover:text-white transition-colors">
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">Contact</h3>
              <ul className="space-y-2">
                <li className="flex items-center text-gray-400">
                  <Mail className="w-4 h-4 mr-2" />
                  <EMAIL>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center">
            <p className="text-gray-400">© 2025 ImgionAI. All rights reserved. Powered by AI.</p>
          </div>
        </div>
      </footer>

      {/* Floating Chatbot */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setChatbotOpen(!chatbotOpen)}
          className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
        >
          <MessageCircle className="w-6 h-6 text-white" />
        </Button>

        {/* Welcome Popup */}
        {showWelcomePopup && (
          <div className="absolute bottom-16 right-0 bg-white rounded-xl shadow-2xl p-4 max-w-xs border border-gray-200">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 mb-1">Hi! I'm FlowBot.</p>
                <p className="text-xs text-gray-600" style={{width: "110px"}}>What would you like to do today?</p>
              </div>
              <Button variant="ghost" size="sm" onClick={() => setShowWelcomePopup(false)} className="p-1 h-auto">
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Chatbot Interface */}
        {chatbotOpen && (
          <div className="absolute bottom-16 right-0 bg-white rounded-xl shadow-2xl w-80 h-96 border border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <span className="font-medium text-gray-900">FlowBot</span>
              </div>
              <Button variant="ghost" size="sm" onClick={() => setChatbotOpen(false)} className="p-1 h-auto">
                <X className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                <div className="bg-gray-100 rounded-lg p-3 max-w-xs">
                  <p className="text-sm text-gray-700">
                    Hi! I can help you find the right tool. What would you like to do with your images?
                  </p>
                </div>
              </div>
            </div>
            <div className="p-4 border-t border-gray-200">
              <div className="flex space-x-2">
                <Input placeholder="Type your message or upload a file..." className="flex-1" />
                <Button size="sm" className="bg-gradient-to-r from-indigo-600 to-purple-600">
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
