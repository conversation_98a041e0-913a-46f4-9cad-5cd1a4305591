// Server-side utilities (Node.js dependencies only)
import sharp from 'sharp';
import { PDFDocument } from 'pdf-lib';
import JSZip from 'jszip';
import { formatFileSize } from './imageUtils';

export interface ProcessedImage {
  buffer: Buffer;
  originalName: string;
  mimetype: string;
  size: number;
}

export interface ConversionOptions {
  quality?: number;
  width?: number;
  height?: number;
  maintainAspectRatio?: boolean;
  pdfPageSize?: 'A4' | 'A3' | 'Letter' | 'Legal';
}

// Supported image formats
const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg', 
  'image/jpg', 
  'image/png', 
  'image/webp', 
  'image/gif', 
  'image/bmp',
  'image/tiff',
  'image/avif',
  'image/heic'
];

/**
 * Validate file (server-side)
 */
export function validateServerFile(file: File): { isValid: boolean; error?: string } {
  const fileSize = file.size;
  const fileType = file.type || file.mimetype;
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  // Check for supported ZIP MIME types
  const supportedZipTypes = [
    'application/zip',
    'application/x-zip-compressed',
    'application/octet-stream'
  ];
  
  const isZipFile = supportedZipTypes.includes(fileType);
  const isImageFile = SUPPORTED_IMAGE_FORMATS.includes(fileType);
  
  if (!isImageFile && !isZipFile) {
    return {
      isValid: false,
      error: `Unsupported file format: ${fileType}`
    };
  }

  if (fileSize > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${MAX_FILE_SIZE / (1024 * 1024)}MB`
    };
  }

  return { isValid: true };
}

/**
 * Process image using Sharp
 */
export async function processImage(
  buffer: Buffer,
  options: ConversionOptions = {}
): Promise<Buffer> {
  let image = sharp(buffer);
  
  // Apply transformations
  if (options.width || options.height) {
    const resizeOptions: { width?: number; height?: number; fit?: string; withoutEnlargement?: boolean } = {};
    
    if (options.width) resizeOptions.width = options.width;
    if (options.height) resizeOptions.height = options.height;
    
    if (options.maintainAspectRatio !== false) {
      resizeOptions.fit = 'inside';
      resizeOptions.withoutEnlargement = true;
    }
    
    image = image.resize(resizeOptions);
  }

  // Convert to JPEG for PDF compatibility
  return await image
    .jpeg({ 
      quality: options.quality || 80,
      mozjpeg: true 
    })
    .toBuffer();
}

/**
 * Convert images to PDF
 */
export async function imagesToPDF(
  images: ProcessedImage[],
  options: ConversionOptions = {}
): Promise<Buffer> {
  const pdfDoc = await PDFDocument.create();

  for (const imageData of images) {
    try {
      // Process image first
      const processedBuffer = await processImage(imageData.buffer, {
        quality: options.quality || 80,
        maintainAspectRatio: true
      });

      // Embed image in PDF
      let image;
      const mimetype = imageData.mimetype;
      
      if (mimetype === 'image/jpeg' || mimetype === 'image/jpg') {
        image = await pdfDoc.embedJpg(processedBuffer);
      } else {
        // Convert to JPEG first for better compatibility
        const jpegBuffer = await sharp(processedBuffer).jpeg({ quality: 80 }).toBuffer();
        image = await pdfDoc.embedJpg(jpegBuffer);
      }

      // Calculate page size based on image dimensions
      const { width: imgWidth, height: imgHeight } = image.scale(1);
      
      // Standard page sizes
      const pageSizes = {
        A4: { width: 595, height: 842 },
        A3: { width: 842, height: 1191 },
        Letter: { width: 612, height: 792 },
        Legal: { width: 612, height: 1008 }
      };

      const pageSize = pageSizes[options.pdfPageSize || 'A4'];
      
      // Calculate scaling to fit image on page with margins
      const margin = 50;
      const maxWidth = pageSize.width - (margin * 2);
      const maxHeight = pageSize.height - (margin * 2);
      
      const scaleX = maxWidth / imgWidth;
      const scaleY = maxHeight / imgHeight;
      const scale = Math.min(scaleX, scaleY, 1); // Don't enlarge
      
      const scaledWidth = imgWidth * scale;
      const scaledHeight = imgHeight * scale;
      
      // Center image on page
      const x = (pageSize.width - scaledWidth) / 2;
      const y = (pageSize.height - scaledHeight) / 2;

      // Add page and draw image
      const page = pdfDoc.addPage([pageSize.width, pageSize.height]);
      page.drawImage(image, {
        x,
        y,
        width: scaledWidth,
        height: scaledHeight,
      });

    } catch (error) {
      console.error(`Error processing image ${imageData.originalName}:`, error);
      throw new Error(`Failed to process image: ${imageData.originalName}`);
    }
  }

  return Buffer.from(await pdfDoc.save());
}



/**
 * Extract images from ZIP file
 */
export async function extractImagesFromZip(zipBuffer: Buffer): Promise<ProcessedImage[]> {
  try {
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(zipBuffer);
    const images: ProcessedImage[] = [];
    const skippedFiles: string[] = [];

    console.log(`Processing ZIP with ${Object.keys(zipContent.files).length} files`);

    for (const [filename, file] of Object.entries(zipContent.files)) {
      // Skip directories and hidden files
      if (file.dir || filename.startsWith('__MACOSX/') || filename.startsWith('.')) {
        continue;
      }

      try {
        const buffer = await file.async('nodebuffer');
        
        // Skip empty files
        if (buffer.length === 0) {
          skippedFiles.push(`${filename} (empty file)`);
          continue;
        }
        
        // Detect MIME type based on file extension
        const ext = filename.toLowerCase().split('.').pop();
        let mimetype = '';
        
        switch (ext) {
          case 'jpg':
          case 'jpeg':
            mimetype = 'image/jpeg';
            break;
          case 'png':
            mimetype = 'image/png';
            break;
          case 'webp':
            mimetype = 'image/webp';
            break;
          case 'gif':
            mimetype = 'image/gif';
            break;
          case 'bmp':
            mimetype = 'image/bmp';
            break;
          case 'tiff':
          case 'tif':
            mimetype = 'image/tiff';
            break;
          case 'avif':
            mimetype = 'image/avif';
            break;
          case 'heic':
            mimetype = 'image/heic';
            break;
          default:
            skippedFiles.push(`${filename} (unsupported format: .${ext})`);
            continue;
        }

        // Validate file size (max 10MB per image)
        const MAX_IMAGE_SIZE = 10 * 1024 * 1024;
        if (buffer.length > MAX_IMAGE_SIZE) {
          skippedFiles.push(`${filename} (file too large: ${formatFileSize(buffer.length)})`);
          continue;
        }

        if (SUPPORTED_IMAGE_FORMATS.includes(mimetype)) {
          images.push({
            buffer,
            originalName: filename.split('/').pop() || filename, // Remove path if nested
            mimetype,
            size: buffer.length
          });
          console.log(`✓ Extracted: ${filename} (${mimetype}, ${formatFileSize(buffer.length)})`);
        }

      } catch (fileError) {
        console.error(`Error processing file ${filename}:`, fileError);
        skippedFiles.push(`${filename} (corrupted or invalid)`);
      }
    }

    // Log skipped files for debugging
    if (skippedFiles.length > 0) {
      console.log(`Skipped ${skippedFiles.length} files:`, skippedFiles);
    }

    // Sort images by filename for consistent ordering
    images.sort((a, b) => a.originalName.localeCompare(b.originalName));

    console.log(`Successfully extracted ${images.length} valid images from ZIP`);
    return images;

  } catch (error) {
    console.error('Failed to extract ZIP file:', error);
    throw new Error('Invalid or corrupted ZIP file');
  }
}

/**
 * Convert image format using Sharp
 */
export async function convertImageFormat(
  buffer: Buffer,
  targetFormat: 'jpg' | 'png' | 'webp' | 'avif' | 'tiff',
  quality: number = 80
): Promise<Buffer> {
  const image = sharp(buffer);

  switch (targetFormat) {
    case 'jpg':
      return await image.jpeg({ quality, mozjpeg: true }).toBuffer();
    case 'png':
      return await image.png({ compressionLevel: 9 }).toBuffer();
    case 'webp':
      return await image.webp({ quality }).toBuffer();
    case 'avif':
      return await image.avif({ quality }).toBuffer();
    case 'tiff':
      return await image.tiff({ compression: 'lzw' }).toBuffer();
    default:
      throw new Error(`Unsupported target format: ${targetFormat}`);
  }
}

/**
 * Convert multiple images and package into ZIP
 */
export async function convertImagesInZip(
  images: ProcessedImage[],
  targetFormat: 'jpg' | 'png' | 'webp' | 'avif' | 'tiff',
  quality: number = 80
): Promise<Buffer> {
  const zip = new JSZip();
  const conversionResults: Array<{
    originalName: string;
    newName: string;
    originalSize: number;
    newSize: number;
  }> = [];

  for (const imageData of images) {
    try {
      // Convert the image
      const convertedBuffer = await convertImageFormat(imageData.buffer, targetFormat, quality);
      
      // Generate new filename
      const originalName = imageData.originalName;
      const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
      const newName = `${nameWithoutExt}.${targetFormat}`;
      
      // Add to ZIP
      zip.file(newName, convertedBuffer);
      
      // Track conversion results
      conversionResults.push({
        originalName,
        newName,
        originalSize: imageData.size,
        newSize: convertedBuffer.length
      });

    } catch (error) {
      console.error(`Error converting image ${imageData.originalName}:`, error);
      throw new Error(`Failed to convert image: ${imageData.originalName}`);
    }
  }

  // Add conversion summary
  const summaryText = conversionResults
    .map(result => 
      `${result.originalName} → ${result.newName} (${formatFileSize(result.originalSize)} → ${formatFileSize(result.newSize)})`
    )
    .join('\n');
  
  zip.file('conversion-summary.txt', summaryText);

  return Buffer.from(await zip.generateAsync({ type: 'nodebuffer' }));
}

