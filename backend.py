from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
import io
from PIL import Image
import uvicorn

app = FastAPI(title="ImageAI Compression Backend")

# Enable CORS for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3001", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_image_format(filename: str) -> str:
    """Get image format from filename"""
    ext = filename.lower().split('.')[-1]
    format_map = {
        'jpg': 'JPEG',
        'jpeg': 'JPEG',
        'png': 'PNG',
        'webp': 'WEBP',
        'bmp': 'BMP',
        'tiff': 'TIFF',
        'tif': 'TIFF'
    }
    return format_map.get(ext, 'JPEG')

def get_mime_type(format_name: str) -> str:
    """Get MIME type from format"""
    mime_map = {
        'JPEG': 'image/jpeg',
        'PNG': 'image/png',
        'WEBP': 'image/webp',
        'BMP': 'image/bmp',
        'TIFF': 'image/tiff'
    }
    return mime_map.get(format_name, 'image/jpeg')

def compress_to_target_kb(image: Image.Image, target_kb: int, output_format: str) -> tuple[bytes, int]:
    """
    Ultra-aggressive compression to achieve exact target size in KB
    Maintains original format
    """
    target_bytes = target_kb * 1024
    
    # Convert image mode based on output format
    if output_format == 'JPEG' and image.mode in ('RGBA', 'LA', 'P'):
        # Create white background for JPEG
        background = Image.new('RGB', image.size, (255, 255, 255))
        if image.mode == 'P':
            image = image.convert('RGBA')
        if image.mode == 'RGBA':
            background.paste(image, mask=image.split()[-1])
        image = background
    elif output_format == 'PNG' and image.mode not in ('RGBA', 'LA', 'P'):
        # Keep transparency support for PNG
        if image.mode == 'RGB':
            image = image.convert('RGBA')
    
    best_result = None
    best_size = float('inf')
    
    # Binary search for optimal quality
    min_quality = 1
    max_quality = 95
    attempts = 0
    max_attempts = 20
    
    while attempts < max_attempts and min_quality <= max_quality:
        current_quality = (min_quality + max_quality) // 2
        
        # Compress with current quality
        buffer = io.BytesIO()
        
        # Save with format-specific options
        save_kwargs = {
            'format': output_format,
            'optimize': True
        }
        
        if output_format in ['JPEG', 'WEBP']:
            save_kwargs['quality'] = current_quality
        elif output_format == 'PNG':
            # PNG compression level (0-9, 9 is highest compression)
            save_kwargs['compress_level'] = min(9, max(0, int(9 * (100 - current_quality) / 100)))
        
        image.save(buffer, **save_kwargs)
        current_size = buffer.tell()
        
        # Track best result
        if abs(current_size - target_bytes) < abs(best_size - target_bytes):
            best_result = buffer.getvalue()
            best_size = current_size
        
        # Adjust search range
        if current_size > target_bytes:
            max_quality = current_quality - 1
        else:
            min_quality = current_quality + 1
        
        # If very close to target, accept it
        if abs(current_size - target_bytes) < target_bytes * 0.05:
            return buffer.getvalue(), current_size
        
        attempts += 1
    
    # Extreme compression fallback
    if best_size > target_bytes * 1.2:
        extreme_qualities = [10, 8, 5, 3, 1] if output_format in ['JPEG', 'WEBP'] else [9, 8, 7, 6, 5]
        
        for quality in extreme_qualities:
            buffer = io.BytesIO()
            
            save_kwargs = {
                'format': output_format,
                'optimize': True
            }
            
            if output_format in ['JPEG', 'WEBP']:
                save_kwargs['quality'] = quality
            elif output_format == 'PNG':
                save_kwargs['compress_level'] = quality
            
            image.save(buffer, **save_kwargs)
            size = buffer.tell()
            
            if size <= target_bytes or abs(size - target_bytes) < abs(best_size - target_bytes):
                best_result = buffer.getvalue()
                best_size = size
                
                if size <= target_bytes:
                    break
    
    return best_result or image.tobytes(), best_size

@app.post("/compress")
async def compress_image(
    file: UploadFile = File(...),
    target_kb: int = Form(...)
):
    """
    Compress image to target KB size while maintaining original format
    """
    try:
        # Read uploaded file
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        
        # Get original format from filename
        output_format = get_image_format(file.filename)
        
        # Get original size
        original_size = len(contents)
        
        # Compress to target size
        compressed_data, final_size = compress_to_target_kb(image, target_kb, output_format)
        
        # Calculate compression ratio
        compression_ratio = ((original_size - final_size) / original_size) * 100
        
        # Return compressed image with proper MIME type
        return Response(
            content=compressed_data,
            media_type=get_mime_type(output_format),
            headers={
                "Content-Disposition": f"attachment; filename=compressed_{file.filename}",
                "X-Original-Size": str(original_size),
                "X-Compressed-Size": str(final_size),
                "X-Compression-Ratio": f"{compression_ratio:.1f}",
                "X-Target-KB": str(target_kb),
                "X-Accuracy": f"{abs(final_size/1024 - target_kb)/target_kb*100:.1f}"
            }
        )
        
    except Exception as e:
        return {"error": f"Compression failed: {str(e)}"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "ImageAI Compression Backend is running"}

if __name__ == "__main__":
    print("🚀 Starting ImageAI Compression Backend...")
    print("📍 Backend URL: http://localhost:8000")
    print("🔗 Frontend should connect to: http://localhost:8000/compress")
    uvicorn.run(app, host="0.0.0.0", port=8000)
