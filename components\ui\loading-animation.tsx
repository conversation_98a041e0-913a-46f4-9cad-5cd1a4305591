"use client"

import React from 'react'
import { Card, CardContent } from './card'
import { Loader2, FileText, CheckCircle, AlertCircle } from 'lucide-react'

interface LoadingAnimationProps {
  status: 'uploading' | 'processing' | 'completed' | 'error'
  progress?: number
  message?: string
  fileName?: string
  className?: string
  completedMessage?: string // Custom message for completed status
}

export function LoadingAnimation({
  status,
  progress = 0,
  message,
  fileName,
  className = '',
  completedMessage
}: LoadingAnimationProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
      case 'processing':
        return <FileText className="h-8 w-8 text-orange-500 animate-pulse" />
      case 'completed':
        return <CheckCircle className="h-8 w-8 text-green-500" />
      case 'error':
        return <AlertCircle className="h-8 w-8 text-red-500" />
      default:
        return <Loader2 className="h-8 w-8 text-gray-500 animate-spin" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'uploading':
        return 'bg-blue-500'
      case 'processing':
        return 'bg-orange-500'
      case 'completed':
        return 'bg-green-500'
      case 'error':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusText = () => {
    if (message) return message
    
    switch (status) {
      case 'uploading':
        return 'Uploading files...'
      case 'processing':
        return 'Converting images to PDF...'
      case 'completed':
        return 'Conversion completed!'
      case 'error':
        return 'Something went wrong'
      default:
        return 'Processing...'
    }
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-8">
        <div className="flex flex-col items-center space-y-6">
          {/* Status Icon */}
          <div className="relative">
            {getStatusIcon()}
            
            {/* Animated Ring */}
            {(status === 'uploading' || status === 'processing') && (
              <div className="absolute inset-0 rounded-full border-4 border-gray-200">
                <div 
                  className={`absolute inset-0 rounded-full border-4 border-transparent ${getStatusColor()} opacity-25 animate-ping`}
                />
              </div>
            )}
          </div>

          {/* Status Text */}
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">
              {getStatusText()}
            </h3>
            
            {fileName && (
              <p className="text-sm text-gray-600">
                {fileName}
              </p>
            )}
          </div>

          {/* Progress Bar */}
          {(status === 'uploading' || status === 'processing') && (
            <div className="w-full max-w-xs">
              <div className="flex justify-between text-xs text-gray-600 mb-1">
                <span>Progress</span>
                <span>{Math.round(progress)}%</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div 
                  className={`h-full ${getStatusColor()} transition-all duration-300 ease-out rounded-full`}
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}

          {/* Processing Steps */}
          {status === 'processing' && (
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="animate-bounce">📁</div>
                <span>→</span>
                <div className="animate-bounce delay-100">🔄</div>
                <span>→</span>
                <div className="animate-bounce delay-200">📄</div>
              </div>
              <p className="text-xs text-gray-500">
                Processing images and creating PDF document
              </p>
            </div>
          )}

          {/* Success Message */}
          {status === 'completed' && (
            <div className="text-center space-y-2">
              <div className="text-2xl">✨</div>
              <p className="text-sm text-gray-600">
                Your PDF is ready for download!
              </p>
            </div>
          )}

          {/* Error Message */}
          {status === 'error' && (
            <div className="text-center space-y-2">
              <div className="text-2xl">⚠️</div>
              <p className="text-sm text-gray-600">
                Please try again or contact support if the issue persists.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 