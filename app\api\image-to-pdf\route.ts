import { NextRequest, NextResponse } from 'next/server';
import { 
  validateServerFile, 
  imagesToPDF, 
  extractImagesFromZip, 
  ProcessedImage,
  ConversionOptions
} from '@/lib/serverUtils';

const SUPPORTED_IMAGE_FORMATS = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/bmp'];

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const options: ConversionOptions = {
      quality: parseInt(formData.get('quality') as string) || 80,
      pdfPageSize: formData.get('pageSize') as 'A4' | 'A3' | 'Letter' | 'Legal' || 'A4'
    };

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files uploaded' },
        { status: 400 }
      );
    }

    const processedImages: ProcessedImage[] = [];

    // Process each uploaded file
    for (const file of files) {
      // Validate file
      const validation = validateServerFile(file);
      if (!validation.isValid) {
        return NextResponse.json(
          { error: validation.error },
          { status: 400 }
        );
      }

      const buffer = Buffer.from(await file.arrayBuffer());

      // Handle ZIP files (support multiple MIME types)
      const isZipFile = ['application/zip', 'application/x-zip-compressed', 'application/octet-stream'].includes(file.type) || 
                        file.name.toLowerCase().endsWith('.zip');
      
      if (isZipFile) {
        try {
          const imagesFromZip = await extractImagesFromZip(buffer);
          
          if (imagesFromZip.length === 0) {
            return NextResponse.json(
              { error: 'No valid image files found inside ZIP archive.' },
              { status: 400 }
            );
          }
          
          processedImages.push(...imagesFromZip);
        } catch (error) {
          console.error('Error extracting ZIP:', error);
          return NextResponse.json(
            { error: 'Failed to extract images from ZIP file. Please ensure it\'s a valid ZIP archive.' },
            { status: 400 }
          );
        }
      }
      // Handle RAR and other unsupported archives
      else if (file.name.toLowerCase().endsWith('.rar') || 
               file.name.toLowerCase().endsWith('.7z') || 
               file.name.toLowerCase().endsWith('.tar') ||
               file.name.toLowerCase().endsWith('.gz')) {
        return NextResponse.json(
          { error: 'Only .zip files are supported.' },
          { status: 400 }
        );
      }
      // Handle individual image files
      else if (SUPPORTED_IMAGE_FORMATS.includes(file.type)) {
        processedImages.push({
          buffer,
          originalName: file.name,
          mimetype: file.type,
          size: file.size
        });
      }
      else {
        return NextResponse.json(
          { error: `Unsupported file type: ${file.type}` },
          { status: 400 }
        );
      }
    }

    // Check if we have any images to process
    if (processedImages.length === 0) {
      return NextResponse.json(
        { error: 'No valid images found in uploaded files' },
        { status: 400 }
      );
    }

    // Convert images to PDF
    const pdfBuffer = await imagesToPDF(processedImages, options);

    // Generate filename for the PDF
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `images-to-pdf-${timestamp}.pdf`;

    // Return the PDF as a downloadable response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Error in image-to-pdf API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error during PDF conversion',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// GET endpoint for API info
export async function GET() {
  return NextResponse.json({
    endpoint: '/api/image-to-pdf',
    method: 'POST',
    description: 'Convert images to PDF',
    acceptedFormats: SUPPORTED_IMAGE_FORMATS,
    maxFileSize: '10MB per file',
    parameters: {
      files: 'File[] - Image files or ZIP containing images',
      quality: 'number (optional) - JPEG quality 1-100, default: 80',
      pageSize: 'string (optional) - A4|A3|Letter|Legal, default: A4'
    },
    response: 'PDF file as download'
  });
} 