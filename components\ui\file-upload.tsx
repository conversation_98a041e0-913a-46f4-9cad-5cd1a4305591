"use client"

import React, { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from './button'
import { Card, CardContent } from './card'
import { Upload, X, FileImage, FileArchive, File, Loader2 } from 'lucide-react'
import { formatFileSize } from '@/lib/imageUtils'

interface FileUploadProps {
  onFilesSelect: (files: File[]) => void
  acceptedTypes?: string[]
  maxFiles?: number
  maxSize?: number
  className?: string
  disabled?: boolean
  showPreview?: boolean
}

interface FileWithPreview extends File {
  preview?: string
}

export function FileUpload({
  onFilesSelect,
  acceptedTypes = ['image/*', '.zip'],
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  className = '',
  disabled = false,
  showPreview = true
}: FileUploadProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [loading, setLoading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setLoading(true)
    
    const filesWithPreview = acceptedFiles.map(file => {
      const fileWithPreview = file as FileWithPreview
      
      // Create preview for images
      if (file.type.startsWith('image/')) {
        fileWithPreview.preview = URL.createObjectURL(file)
      }
      
      return fileWithPreview
    })

    setFiles(prev => [...prev, ...filesWithPreview])
    onFilesSelect([...files, ...acceptedFiles])
    setLoading(false)
  }, [files, onFilesSelect])

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index)
    setFiles(newFiles)
    onFilesSelect(newFiles)
    
    // Revoke preview URL to prevent memory leaks
    if (files[index].preview) {
      URL.revokeObjectURL(files[index].preview!)
    }
  }

  const clearAll = () => {
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview)
      }
    })
    setFiles([])
    onFilesSelect([])
  }

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => {
      if (type === '.zip') {
        acc['application/zip'] = ['.zip']
        acc['application/x-zip-compressed'] = ['.zip']
        acc['application/octet-stream'] = ['.zip']
      } else if (type === 'image/*') {
        acc['image/*'] = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff', '.tif', '.avif', '.heic']
      } else {
        acc[type] = []
      }
      return acc
    }, {} as Record<string, string[]>),
    maxFiles: maxFiles - files.length,
    maxSize,
    disabled: disabled || loading
  })

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <FileImage className="h-8 w-8 text-blue-500" />
    } else if (['application/zip', 'application/x-zip-compressed', 'application/octet-stream'].includes(file.type) || 
               file.name.toLowerCase().endsWith('.zip')) {
      return <FileArchive className="h-8 w-8 text-orange-500" />
    }
    return <File className="h-8 w-8 text-gray-500" />
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
          ${isDragActive && !isDragReject ? 'border-blue-500 bg-blue-50' : ''}
          ${isDragReject ? 'border-red-500 bg-red-50' : ''}
          ${!isDragActive ? 'border-gray-300 hover:border-gray-400' : ''}
          ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center justify-center space-y-4">
          {loading ? (
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />
          ) : (
            <Upload className="h-12 w-12 text-gray-400" />
          )}
          
          <div>
            <p className="text-lg font-medium text-gray-700">
              {isDragActive ? 'Drop files here...' : 'Drag & drop files here'}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              or click to select files
            </p>
          </div>
          
          <div className="text-xs text-gray-400 space-y-1">
            <p>Supported: Images (JPG, PNG, WebP, GIF, BMP, TIFF, AVIF), ZIP files</p>
            <p>Max size: {formatFileSize(maxSize)} per file</p>
            <p>Max files: {maxFiles}</p>
          </div>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium text-gray-900">
                Selected Files ({files.length})
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAll}
                disabled={loading}
              >
                Clear All
              </Button>
            </div>
            
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {files.map((file, index) => (
                <div
                  key={`${file.name}-${index}`}
                  className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
                >
                  {/* File Icon/Preview */}
                  <div className="flex-shrink-0">
                    {showPreview && file.preview ? (
                      <img
                        src={file.preview}
                        alt={file.name}
                        className="h-12 w-12 object-cover rounded"
                      />
                    ) : (
                      getFileIcon(file)
                    )}
                  </div>
                  
                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                  
                  {/* Remove Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(index)}
                    disabled={loading}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 