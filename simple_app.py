import streamlit as st
import io
from PIL import Image

# Page config
st.set_page_config(
    page_title="ImageAI - Simple Compressor",
    page_icon="🖼️",
    layout="wide"
)

def compress_image_to_target_kb(image, target_kb):
    """
    Ultra-aggressive compression to achieve exact target size in KB
    """
    target_bytes = target_kb * 1024
    
    # Convert to RGB if needed
    if image.mode in ('RGBA', 'LA', 'P'):
        image = image.convert('RGB')
    
    best_result = None
    best_size = float('inf')
    
    # Try multiple quality levels with binary search
    min_quality = 1
    max_quality = 95
    attempts = 0
    max_attempts = 20
    
    st.write(f"🎯 **Target:** {target_kb}KB ({target_bytes} bytes)")
    progress_bar = st.progress(0)
    
    while attempts < max_attempts and min_quality <= max_quality:
        current_quality = (min_quality + max_quality) // 2
        
        # Compress with current quality
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG', quality=current_quality, optimize=True)
        current_size = buffer.tell()
        
        # Track best result
        if abs(current_size - target_bytes) < abs(best_size - target_bytes):
            best_result = buffer.getvalue()
            best_size = current_size
        
        # Show progress
        st.write(f"🔄 **Attempt {attempts+1}:** Quality={current_quality}%, Size={current_size/1024:.1f}KB")
        
        # Adjust search range
        if current_size > target_bytes:
            max_quality = current_quality - 1
        else:
            min_quality = current_quality + 1
        
        # If very close to target, accept it
        if abs(current_size - target_bytes) < target_bytes * 0.05:
            st.write(f"✅ **Perfect match found!** Size={current_size/1024:.1f}KB")
            progress_bar.progress(1.0)
            return buffer.getvalue(), current_size
        
        attempts += 1
        progress_bar.progress(attempts / max_attempts)
    
    # If still not close enough, try extreme compression
    if best_size > target_bytes * 1.2:
        st.write("🔥 **Trying extreme compression...**")
        for quality in [10, 8, 5, 3, 1]:
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=quality, optimize=True)
            size = buffer.tell()
            
            st.write(f"🔥 **Extreme:** Quality={quality}%, Size={size/1024:.1f}KB")
            
            if size <= target_bytes or abs(size - target_bytes) < abs(best_size - target_bytes):
                best_result = buffer.getvalue()
                best_size = size
                
                if size <= target_bytes:
                    st.write(f"✅ **Target achieved with extreme compression!**")
                    break
    
    progress_bar.progress(1.0)
    return best_result, best_size

def main():
    st.markdown("# 🖼️ ImageAI - Python Compressor")
    st.markdown("### 🗜️ Ultra-Aggressive Image Compression")
    
    # File uploader
    uploaded_file = st.file_uploader(
        "Upload Image",
        type=['png', 'jpg', 'jpeg', 'webp', 'bmp'],
        help="Drag and drop an image here or click to browse"
    )
    
    if uploaded_file:
        # Show original image
        image = Image.open(uploaded_file)
        original_size = len(uploaded_file.getvalue())
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Original Image:**")
            st.image(image, width=300)
            st.write(f"**Size:** {original_size/1024:.1f} KB")
            st.write(f"**Dimensions:** {image.size[0]}x{image.size[1]}")
        
        with col2:
            # Compression settings
            target_kb = st.number_input(
                "Target Size (KB)",
                min_value=10,
                max_value=5000,
                value=100,
                step=10,
                help="Exact target file size in KB"
            )
            
            if st.button("🚀 Compress Image", type="primary"):
                st.write("---")
                st.write("## 🔄 Compression Process:")
                
                # Compress
                compressed_data, final_size = compress_image_to_target_kb(image, target_kb)
                
                # Results
                compression_ratio = ((original_size - final_size) / original_size) * 100
                
                st.write("---")
                st.write("## 📊 Results:")
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Original", f"{original_size/1024:.1f} KB")
                with col2:
                    st.metric("Compressed", f"{final_size/1024:.1f} KB")
                with col3:
                    st.metric("Saved", f"{compression_ratio:.1f}%")
                
                # Success indicator
                accuracy = abs(final_size/1024 - target_kb) / target_kb * 100
                if accuracy <= 10:
                    st.success(f"🎯 **Excellent!** Target achieved within {accuracy:.1f}% accuracy!")
                elif accuracy <= 20:
                    st.warning(f"⚠️ **Good!** Close to target (±{accuracy:.1f}%)")
                else:
                    st.error(f"❌ **Needs improvement** - {accuracy:.1f}% off target")
                
                # Show compressed image
                compressed_image = Image.open(io.BytesIO(compressed_data))
                st.write("**Compressed Image:**")
                st.image(compressed_image, width=300)
                
                # Download button
                filename = f"compressed_{uploaded_file.name.split('.')[0]}.jpg"
                
                st.download_button(
                    label=f"📥 Download {filename}",
                    data=compressed_data,
                    file_name=filename,
                    mime="image/jpeg"
                )

if __name__ == "__main__":
    main()
