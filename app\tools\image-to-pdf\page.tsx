"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { FileUpload } from '@/components/ui/file-upload'
import { LoadingAnimation } from '@/components/ui/loading-animation'
import { 
  FileText, 
  Download, 
  ArrowLeft, 
  Settings, 
  Sparkles,
  Check,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'

interface ConversionState {
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error'
  progress: number
  message?: string
  downloadUrl?: string
  fileName?: string
}

export default function ImageToPDFPage() {
  const [files, setFiles] = useState<File[]>([])
  const [conversionState, setConversionState] = useState<ConversionState>({
    status: 'idle',
    progress: 0
  })
  const [settings, setSettings] = useState({
    quality: 80,
    pageSize: 'A4'
  })

  const handleFilesSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles)
    setConversionState({
      status: 'idle',
      progress: 0
    })
  }

  const convertToPDF = async () => {
    if (files.length === 0) return

    try {
      setConversionState({
        status: 'uploading',
        progress: 10,
        message: 'Preparing files...'
      })

      // Create FormData
      const formData = new FormData()
      files.forEach(file => {
        formData.append('files', file)
      })
      formData.append('quality', settings.quality.toString())
      formData.append('pageSize', settings.pageSize)

      setConversionState({
        status: 'processing',
        progress: 30,
        message: 'Uploading and processing...'
      })

      // Call API
      const response = await fetch('/api/image-to-pdf', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Conversion failed')
      }

      setConversionState({
        status: 'processing',
        progress: 70,
        message: 'Creating PDF document...'
      })

      // Get the PDF blob
      const blob = await response.blob()
      const downloadUrl = URL.createObjectURL(blob)

      // Extract filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition')
      const fileName = contentDisposition 
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : `images-to-pdf-${Date.now()}.pdf`

      setConversionState({
        status: 'completed',
        progress: 100,
        message: 'PDF created successfully!',
        downloadUrl,
        fileName
      })

    } catch (error: unknown) {
      console.error('Conversion error:', error)
      setConversionState({
        status: 'error',
        progress: 0,
        message: error.message || 'Failed to convert images to PDF'
      })
    }
  }

  const downloadPDF = () => {
    if (conversionState.downloadUrl && conversionState.fileName) {
      const link = document.createElement('a')
      link.href = conversionState.downloadUrl
      link.download = conversionState.fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const resetConverter = () => {
    setFiles([])
    setConversionState({
      status: 'idle',
      progress: 0
    })
    if (conversionState.downloadUrl) {
      URL.revokeObjectURL(conversionState.downloadUrl)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                href="/" 
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Back to Home</span>
              </Link>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Imgion AI</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <FileText className="w-7 h-7 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Image to PDF Converter</h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Convert your images into a single PDF document instantly. 
            Upload individual images or ZIP files containing multiple images.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Upload & Settings */}
          <div className="lg:col-span-2 space-y-6">
            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Upload Images</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onFilesSelect={handleFilesSelect}
                  acceptedTypes={['image/*', '.zip']}
                  maxFiles={20}
                  disabled={conversionState.status === 'processing' || conversionState.status === 'uploading'}
                />
              </CardContent>
            </Card>

            {/* Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>Conversion Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Quality Setting */}
                  <div className="space-y-2">
                    <Label htmlFor="quality">Image Quality</Label>
                    <Select 
                      value={settings.quality.toString()} 
                      onValueChange={(value) => setSettings(prev => ({ ...prev, quality: parseInt(value) }))}
                      disabled={conversionState.status === 'processing' || conversionState.status === 'uploading'}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="60">Low Quality (60%)</SelectItem>
                        <SelectItem value="80">Standard Quality (80%)</SelectItem>
                        <SelectItem value="90">High Quality (90%)</SelectItem>
                        <SelectItem value="100">Maximum Quality (100%)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Page Size Setting */}
                  <div className="space-y-2">
                    <Label htmlFor="pageSize">PDF Page Size</Label>
                    <Select 
                      value={settings.pageSize} 
                      onValueChange={(value) => setSettings(prev => ({ ...prev, pageSize: value }))}
                      disabled={conversionState.status === 'processing' || conversionState.status === 'uploading'}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="A4">A4 (210 × 297 mm)</SelectItem>
                        <SelectItem value="A3">A3 (297 × 420 mm)</SelectItem>
                        <SelectItem value="Letter">Letter (8.5 × 11 inch)</SelectItem>
                        <SelectItem value="Legal">Legal (8.5 × 14 inch)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={convertToPDF}
                disabled={files.length === 0 || conversionState.status === 'processing' || conversionState.status === 'uploading'}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3"
                size="lg"
              >
                {conversionState.status === 'processing' || conversionState.status === 'uploading' ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Converting...
                  </>
                ) : (
                  <>
                    <FileText className="h-5 w-5 mr-2" />
                    Convert to PDF ({files.length} files)
                  </>
                )}
              </Button>

              {conversionState.status === 'completed' && (
                <Button
                  onClick={downloadPDF}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3"
                  size="lg"
                >
                  <Download className="h-5 w-5 mr-2" />
                  Download PDF
                </Button>
              )}

              {(conversionState.status === 'completed' || conversionState.status === 'error') && (
                <Button
                  onClick={resetConverter}
                  variant="outline"
                  className="font-medium py-3"
                  size="lg"
                >
                  Convert Another
                </Button>
              )}
            </div>
          </div>

          {/* Right Column - Status & Preview */}
          <div className="space-y-6">
            {/* Processing Status */}
            {(conversionState.status !== 'idle') && (
              <LoadingAnimation
                status={conversionState.status}
                progress={conversionState.progress}
                message={conversionState.message}
                fileName={conversionState.fileName}
              />
            )}

            {/* Features Info */}
            {conversionState.status === 'idle' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">Multiple Images</p>
                      <p className="text-xs text-gray-600">Combine multiple images into one PDF</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">ZIP Support</p>
                      <p className="text-xs text-gray-600">Upload ZIP files with images</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">High Quality</p>
                      <p className="text-xs text-gray-600">Maintains original image quality</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">Custom Settings</p>
                      <p className="text-xs text-gray-600">Adjust quality and page size</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Tips */}
            {files.length === 0 && conversionState.status === 'idle' && (
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-blue-900">Tips for best results:</h4>
                      <ul className="text-sm text-blue-800 mt-2 space-y-1">
                        <li>• Use high-resolution images for better PDF quality</li>
                        <li>• Images will be automatically resized to fit the page</li>
                        <li>• ZIP files should contain only image files</li>
                        <li>• Maximum 10MB per file, 20 files total</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  )
} 