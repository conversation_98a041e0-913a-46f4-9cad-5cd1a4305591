// Client-side utility functions (no Node.js dependencies)

// Supported image formats
export const SUPPORTED_IMAGE_FORMATS = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/bmp'];
export const SUPPORTED_FILE_FORMATS = [...SUPPORTED_IMAGE_FORMATS, 'application/pdf', 'application/zip'];

// File size limits (in bytes)
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_TOTAL_SIZE = 50 * 1024 * 1024; // 50MB for bulk operations

export interface ProcessedImage {
  buffer: Buffer;
  originalName: string;
  mimetype: string;
  size: number;
}

export interface ConversionOptions {
  quality?: number;
  width?: number;
  height?: number;
  maintainAspectRatio?: boolean;
  pdfPageSize?: 'A4' | 'A3' | 'Letter' | 'Legal';
}

/**
 * Validate file type and size (client-side)
 */
export function validateFile(file: File): { isValid: boolean; error?: string } {
  const fileSize = file.size;
  const fileType = file.type;

  if (!SUPPORTED_FILE_FORMATS.includes(fileType)) {
    return {
      isValid: false,
      error: `Unsupported file format. Supported formats: JPG, PNG, WebP, GIF, BMP, PDF, ZIP`
    };
  }

  if (fileSize > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${MAX_FILE_SIZE / (1024 * 1024)}MB`
    };
  }

  return { isValid: true };
}

/**
 * Convert file size to human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Generate unique filename
 */
export function generateFileName(originalName: string, suffix: string = ''): string {
  const timestamp = Date.now();
  const extension = originalName.split('.').pop();
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  
  return `${nameWithoutExt}${suffix}_${timestamp}.${extension}`;
}

/**
 * Check if file is image
 */
export function isImageFile(file: File): boolean {
  return SUPPORTED_IMAGE_FORMATS.includes(file.type);
}

/**
 * Check if file is ZIP
 */
export function isZipFile(file: File): boolean {
  return file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip');
}

/**
 * Get file extension
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
} 