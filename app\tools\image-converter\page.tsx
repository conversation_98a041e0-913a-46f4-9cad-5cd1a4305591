"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { FileUpload } from '@/components/ui/file-upload'
import { LoadingAnimation } from '@/components/ui/loading-animation'
import { QualitySlider } from '@/components/ui/quality-slider'
import { 
  RefreshCw, 
  Download, 
  ArrowLeft, 
  Settings, 
  Sparkles,
  Check,
  AlertCircle,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

interface ConversionState {
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error'
  progress: number
  message?: string
  downloadUrl?: string
  fileName?: string
}

interface ConversionResult {
  originalName: string
  newName: string
  originalFormat: string
  targetFormat: string
  originalSize: number
  newSize: number
}

const TARGET_FORMATS = [
  { value: 'jpg', label: 'JPEG (.jpg)', description: 'Best for photos with compression' },
  { value: 'png', label: 'PNG (.png)', description: 'Best for graphics with transparency' },
  { value: 'webp', label: 'WebP (.webp)', description: 'Modern format, smaller file sizes' },
  { value: 'avif', label: 'AVIF (.avif)', description: 'Next-gen format, excellent compression' },
  { value: 'tiff', label: 'TIFF (.tiff)', description: 'High quality, lossless compression' },
]

export default function ImageConverterPage() {
  const [files, setFiles] = useState<File[]>([])
  const [conversionState, setConversionState] = useState<ConversionState>({
    status: 'idle',
    progress: 0
  })
  const [settings, setSettings] = useState({
    targetFormat: 'jpg',
    quality: 80
  })
  const [conversionResults, setConversionResults] = useState<ConversionResult[]>([])

  const handleFilesSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles)
    setConversionState({
      status: 'idle',
      progress: 0
    })
    setConversionResults([])
  }

  const convertImages = async () => {
    if (files.length === 0) return

    try {
      setConversionState({
        status: 'uploading',
        progress: 10,
        message: 'Preparing files...'
      })

      // Create FormData
      const formData = new FormData()
      files.forEach(file => {
        formData.append('files', file)
      })
      formData.append('targetFormat', settings.targetFormat)
      formData.append('quality', settings.quality.toString())

      setConversionState({
        status: 'processing',
        progress: 30,
        message: 'Converting images...'
      })

      // Call API
      const response = await fetch('/api/image-converter', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Conversion failed')
      }

      setConversionState({
        status: 'processing',
        progress: 70,
        message: 'Finalizing conversion...'
      })

      // Get the converted file(s)
      const blob = await response.blob()
      const downloadUrl = URL.createObjectURL(blob)

      // Extract filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition')
      const fileName = contentDisposition 
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : `converted-images-${settings.targetFormat}-${Date.now()}.${files.length > 1 ? 'zip' : settings.targetFormat}`

      // Create conversion results for display
      const results: ConversionResult[] = files.map(file => ({
        originalName: file.name,
        newName: file.name.replace(/\.[^/.]+$/, `.${settings.targetFormat}`),
        originalFormat: file.name.split('.').pop()?.toUpperCase() || 'Unknown',
        targetFormat: settings.targetFormat.toUpperCase(),
        originalSize: file.size,
        newSize: 0 // We don't know the exact new size, will be estimated
      }))

      setConversionResults(results)
      setConversionState({
        status: 'completed',
        progress: 100,
        message: 'Conversion completed successfully!',
        downloadUrl,
        fileName
      })

    } catch (error: unknown) {
      console.error('Conversion error:', error)
      setConversionState({
        status: 'error',
        progress: 0,
        message: error.message || 'Failed to convert images'
      })
    }
  }

  const downloadFiles = () => {
    if (conversionState.downloadUrl && conversionState.fileName) {
      const link = document.createElement('a')
      link.href = conversionState.downloadUrl
      link.download = conversionState.fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const resetConverter = () => {
    setFiles([])
    setConversionResults([])
    setConversionState({
      status: 'idle',
      progress: 0
    })
    if (conversionState.downloadUrl) {
      URL.revokeObjectURL(conversionState.downloadUrl)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const selectedFormat = TARGET_FORMATS.find(f => f.value === settings.targetFormat)
  const isLossyFormat = ['jpg', 'webp', 'avif'].includes(settings.targetFormat)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                href="/" 
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>Back to Home</span>
              </Link>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Imgion AI</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
              <RefreshCw className="w-7 h-7 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Convert Image Format Instantly</h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Upload images or zip files, select target format, and convert instantly. 
            Support for JPG, PNG, WebP, AVIF, TIFF, and more formats.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Upload & Settings */}
          <div className="lg:col-span-2 space-y-6">
            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <RefreshCw className="h-5 w-5" />
                  <span>Upload Images</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onFilesSelect={handleFilesSelect}
                  acceptedTypes={['image/*', '.zip']}
                  maxFiles={20}
                  disabled={conversionState.status === 'processing' || conversionState.status === 'uploading'}
                />
              </CardContent>
            </Card>

            {/* Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>Conversion Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Target Format */}
                <div className="space-y-2">
                  <Label htmlFor="targetFormat">Target Format</Label>
                  <Select 
                    value={settings.targetFormat} 
                    onValueChange={(value) => setSettings(prev => ({ ...prev, targetFormat: value }))}
                    disabled={conversionState.status === 'processing' || conversionState.status === 'uploading'}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TARGET_FORMATS.map(format => (
                        <SelectItem key={format.value} value={format.value}>
                          <div>
                            <div className="font-medium">{format.label}</div>
                            <div className="text-xs text-gray-500">{format.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedFormat && (
                    <p className="text-sm text-gray-600">{selectedFormat.description}</p>
                  )}
                </div>

                {/* Quality Slider (only for lossy formats) */}
                {isLossyFormat && (
                  <QualitySlider
                    value={settings.quality}
                    onChange={(value) => setSettings(prev => ({ ...prev, quality: value }))}
                    disabled={conversionState.status === 'processing' || conversionState.status === 'uploading'}
                    label="Output Quality"
                  />
                )}
              </CardContent>
            </Card>

            {/* Conversion Results */}
            {conversionResults.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Conversion Results</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {conversionResults.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="text-sm">
                            <span className="font-medium text-gray-900">{result.originalName}</span>
                            <ArrowRight className="h-4 w-4 mx-2 inline text-gray-400" />
                            <span className="font-medium text-green-600">{result.newName}</span>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          {result.originalFormat} → {result.targetFormat}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={convertImages}
                disabled={files.length === 0 || conversionState.status === 'processing' || conversionState.status === 'uploading'}
                className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-medium py-3"
                size="lg"
              >
                {conversionState.status === 'processing' || conversionState.status === 'uploading' ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Converting...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-5 w-5 mr-2" />
                    Convert to {settings.targetFormat.toUpperCase()} ({files.length} files)
                  </>
                )}
              </Button>

              {conversionState.status === 'completed' && (
                <Button
                  onClick={downloadFiles}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3"
                  size="lg"
                >
                  <Download className="h-5 w-5 mr-2" />
                  Download {files.length > 1 ? 'ZIP' : 'Image'}
                </Button>
              )}

              {(conversionState.status === 'completed' || conversionState.status === 'error') && (
                <Button
                  onClick={resetConverter}
                  variant="outline"
                  className="font-medium py-3"
                  size="lg"
                >
                  Convert Another
                </Button>
              )}
            </div>
          </div>

          {/* Right Column - Status & Info */}
          <div className="space-y-6">
            {/* Processing Status */}
            {(conversionState.status !== 'idle') && (
              <LoadingAnimation
                status={conversionState.status}
                progress={conversionState.progress}
                message={conversionState.message}
                fileName={conversionState.fileName}
              />
            )}

            {/* Format Info */}
            {conversionState.status === 'idle' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Supported Formats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {TARGET_FORMATS.map(format => (
                    <div key={format.value} className="flex items-start space-x-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-sm">{format.label}</p>
                        <p className="text-xs text-gray-600">{format.description}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Features & Tips */}
            {files.length === 0 && conversionState.status === 'idle' && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-sm">Batch Conversion</p>
                        <p className="text-xs text-gray-600">Convert multiple images at once</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-sm">ZIP Support</p>
                        <p className="text-xs text-gray-600">Upload ZIP files with images</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-sm">Quality Control</p>
                        <p className="text-xs text-gray-600">Adjust compression for optimal results</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-sm">Modern Formats</p>
                        <p className="text-xs text-gray-600">Support for WebP, AVIF, and more</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-medium text-blue-900">Pro Tips:</h4>
                        <ul className="text-sm text-blue-800 mt-2 space-y-1">
                          <li>• WebP offers best compression for web images</li>
                          <li>• AVIF is the newest format with excellent quality</li>
                          <li>• Use PNG for images with transparency</li>
                          <li>• TIFF is best for professional photography</li>
                          <li>• ZIP files will be extracted and converted automatically</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>
      </main>
    </div>
  )
} 